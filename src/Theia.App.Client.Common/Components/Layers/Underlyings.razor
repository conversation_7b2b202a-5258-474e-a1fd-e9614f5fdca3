@using Theia.App.Client.Common.ViewModels
@using Theia.Infrastructure.Common.Defaults

<TelerikCard>
    <CardHeader>
        <CardTitle>
            @Resource.Underlying
        </CardTitle>
    </CardHeader>
    <CardBody>
        <TelerikGrid
            TItem="UnderlyingVm"
            Data="@underlyings"
            Pageable="true"
            Sortable="true"
            Reorderable="true"
            ShowColumnMenu="true"
            @bind-PageSize="@pageSize"
            Size="@ThemeConstants.Grid.Size.Medium"
            EnableLoaderContainer="@DefaultSettings.Grid.IsLoaderVisible">
            <GridSettings>
                <GridPagerSettings PageSizes="@DefaultSettings.Pager.PageSizes" ButtonCount="@DefaultSettings.Pager.ButtonCount"/>
            </GridSettings>
            <GridColumns>
                <GridColumn Field="@nameof(UnderlyingVm.Layer)" Title="@Resource.Layer"/>
                <GridColumn Field="@nameof(UnderlyingVm.LimitDisplay)" Title="@Resource.Limit"/>
                <GridColumn Field="@nameof(UnderlyingVm.ExcessDisplay)" Title="@Resource.Excess"/>
                <GridColumn Field="@nameof(UnderlyingVm.PremiumDisplay)" Title="@Resource.Premium"/>
                <GridColumn Field="@nameof(UnderlyingVm.RPMDisplay)" Title="@Resource.Rate_per_million" >
                    <HeaderTemplate>
                        <span>@Resource.RPM</span>
                    </HeaderTemplate>
                </GridColumn>
                <GridColumn Field="@nameof(UnderlyingVm.ILFDisplay)" Title="@Resource.Increased_limit_factor">
                    <HeaderTemplate>
                        <span>@Resource.ILF</span>
                    </HeaderTemplate>
                </GridColumn>
                <GridColumn Field="@nameof(UnderlyingVm.Status)" Title="@Resource.Status">
                    <Template>
                        @if (context is UnderlyingVm vm)
                        {
                            <TelerikChip
                                Class="app-width-fit-content"
                                Size="@ThemeConstants.Chip.Size.Small"
                                ThemeColor="@vm.StatusChipColor"
                                Text="@vm.StatusText"/>
                        }
                    </Template>
                </GridColumn>
                <GridColumn Field="@nameof(UnderlyingVm.Insurer)" Title="@Resource.Insurer"/>
            </GridColumns>
            <DetailTemplate>
                @if (context is { Parts.Length: > 0, IsPartPlacement: true })
                {
                    <TelerikGrid TItem="UnderlyingPartVm"
                                 Data="@context.Parts"
                                 Pageable="false"
                                 Sortable="true"
                                 Size="@ThemeConstants.Grid.Size.Small">
                        <GridColumns>
                            <GridColumn Field="@nameof(UnderlyingPartVm.Part)" Title="@Resource.Part">
                                <Template Context="part">
                                    @if (part is UnderlyingPartVm vm)
                                    {
                                        if (vm.IsQuotaShare)
                                        {
                                            <span style="display: flex; align-items: center;">
                                                <div>@vm.Part</div>
                                                <iconify-icon class="tooltip-target grey-icon" 
                                                              title="@Resource.Quota_Share" 
                                                              icon="material-symbols:pie-chart"/>
                                            </span>
                                        }
                                        else
                                        {
                                            @vm.Part 
                                        }
                                    }
                                </Template> 
                            </GridColumn> 
                            <GridColumn Field="@nameof(UnderlyingPartVm.LineSizeDisplay)" Title="@Resource.Line_Size"/>
                            <GridColumn Field="@nameof(UnderlyingPartVm.PremiumDisplay)" Title="@Resource.Premium"/>
                            <GridColumn Field="@nameof(UnderlyingPartVm.Insurer)" Title="@Resource.Insurer"/> 
                        </GridColumns>
                    </TelerikGrid>
                }
                else
                {
                    @Resource.No_selected_options_in_layer 
                }
            </DetailTemplate>
        </TelerikGrid>
    </CardBody>
</TelerikCard>

<style>
    .grey-icon {
        var(--theia-gray);
    }
</style>