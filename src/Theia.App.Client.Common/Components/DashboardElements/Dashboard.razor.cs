using Microsoft.AspNetCore.Components;
using Theia.App.Client.Common.Consumers;
using Theia.App.Client.Common.Interfaces;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Models.Submission.Wholesale;
using Theia.App.Client.Common.Services;
using Theia.App.Client.Common.Services.Breadcrumbs;
using Theia.App.Shared.DTOs.WholesaleSubmissions;
using Theia.App.Shared.Layers;
using Theia.App.Shared.Models;
using Theia.Domain.Common.Enums;
using Theia.Http.Services;

namespace Theia.App.Client.Common.Components.DashboardElements;

public partial class Dashboard : IDisposable
{
    [Parameter]
    public string? UrlSafeSubmissionId { get; set; }

    [Parameter]
    public string? UrlSafeBrokerSubmissionId { get; set; }

    [Parameter]
    public string? UrlSafeOrganisationId { get; set; }

    [Inject]
    private ICommonClient CommonClient { get; set; } = null!;

    [Inject]
    private WholesaleClient WholesaleClient { get; init; } = null!;

    [Inject]
    private IBreadcrumbService BreadcrumbService { get; set; } = null!;

    private bool ShowBasicSubmission => string.IsNullOrWhiteSpace(UrlSafeSubmissionId);
    private Guid SubmissionId => (WebSafeGuid)(UrlSafeSubmissionId ?? string.Empty);
    
    private readonly CancellationTokenSource cts = new();

    private DashboardDataModel? dashboardData;
    private WholesaleSubmissionInfoModel? wholesaleSubmissionInfo;
    private bool isLoading;
    private bool canViewSubmission;
    private string? percentilePosition;

    protected override async Task OnInitializedAsync()
    {
        List<Task> tasks = [LoadInitialDataAsync(), MarkSubmissionAsViewedAsync()];
        TabNames = ["Submission", "Layers", "ProgramStack"];
        await LoadInitialDataAsync().ConfigureAwait(true);
        await MarkSubmissionAsViewedAsync();

        if (!ShowBasicSubmission)
        {
            tasks.Add(LoadPercentileScoreForOrganisationAsync());
        }

        await Task.WhenAll(tasks);
        
        await base.OnInitializedAsync();
    }

    [Inject]
    private ISubmissionsClient SubmissionsClient { get; init; } = null!;
    
    private async Task MarkSubmissionAsViewedAsync()
    {
        if (!string.IsNullOrWhiteSpace(UrlSafeSubmissionId))
        {
            NoPayloadApiResponse response = 
                await SubmissionsClient
                    .MarkSubmissionAsRead(UrlSafeSubmissionId, cts.Token)
                    .ConfigureAwait(false);

            await NotificationHelper
                .HandleNoPayloadResponseWithFuncAsync(response, showSuccess: false)
                .ConfigureAwait(false);
        }
    }

    private async Task LoadInitialDataAsync()
    {
        isLoading = true;
        if (!string.IsNullOrWhiteSpace(UrlSafeSubmissionId))
        {
            ApiResponse<DashboardDataModel> response = await CommonClient
                .RetrieveDashboardData(UrlSafeSubmissionId, cts.Token)
                .ConfigureAwait(false);

            await NotificationHelper.HandleResultWithFuncAsync(response, async success => await LoadSubmissionDataAsync(success));
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadSubmissionDataAsync(DashboardDataModel data)
    {
        dashboardData = data;
        if(dashboardData.OrganisationInformation.SubmissionType is SubmissionType.WholesaleBrokerSubmission)
        {
            ApiResponse<WholesaleSubmissionInfoResponse> basicWholesaleSubmissionInfo = await CommonClient
                .RetrieveWholesaleSubmissionInfoAsync((WebSafeGuid)UrlSafeSubmissionId!, cts.Token);
            
            NotificationHelper.HandleResultWithAction(basicWholesaleSubmissionInfo, success => LoadBasicWholesaleSubmissionData(success.Result), showSuccess: false);
        }
        canViewSubmission = true;
    }
    
    private void LoadBasicWholesaleSubmissionData(WholesaleSubmissionInfoResponse data)
    {
        wholesaleSubmissionInfo = new WholesaleSubmissionInfoModel
        {
            LayerName = LayerNamingService.Generate(data.LayerPosition, data.Currency, data.Limit, data.Excess),
            Limit = data.Limit,
            Excess = data.Excess,
            TargetPremium = data.TargetPremium,
            IsSharedWithBroker = data.IsSharedWithBroker,
            Currency = data.Currency,
            LayerPosition = data.LayerPosition,
            IndicationRequestId = data.IndicationRequestId,
            LayerId = data.LayerId,
            ShowUnderlying = data.ShowUnderlying,
            IsUserPartOfRequestingTenant = data.IsUserPartOfRequestingTenant,
            IsUserPartOfOrganisation = data.IsUserPartOfOrganisation,
            IsUserPartOfWholesaleBroker = data.IsUserPartOfWholesaleBroker,
            IsApproved = data.IsApproved,
            SetApprovalState = SetApprovalStateAsync,
            IsShareableWithRetail = data.IsShareableWithRetail
        };
        StateHasChanged();
    }

    private async Task SetApprovalStateAsync(bool isApproved)
    {
        SetWholesaleSubmissionApprovalStateRequest request = new()
        {
            WholesaleSubmissionId = (WebSafeGuid)UrlSafeSubmissionId!,
            IsApproved = isApproved
        };

        NoPayloadApiResponse response = await WholesaleClient
            .SetWholesaleSubmissionApprovalStateRequestAsync(request, cts.Token);

        NotificationHelper.HandleResultWithAction(response, _ =>
        {
            if (wholesaleSubmissionInfo is not null)
            {
                wholesaleSubmissionInfo.IsApproved = isApproved;
            }
        });
    }

    private async Task LoadPercentileScoreForOrganisationAsync()
    {
        ApiResponse<string> percentileResponse =
            await CommonClient
                .RetrievePercentilePositionForSubmission(UrlSafeSubmissionId!, cts.Token);

        NotificationHelper.HandleResultWithAction(percentileResponse, success => percentilePosition = success.Result);

        StateHasChanged();
    }

    public new void Dispose()
    {
        cts.Cancel();
        cts.Dispose();
    }
}