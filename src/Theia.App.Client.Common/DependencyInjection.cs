using Blazored.LocalStorage;
using Fluxor;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using Theia.App.Client.Admin.Shared;
using Theia.App.Client.Common.Consumers;
using Theia.App.Client.Common.Custom;
using Theia.App.Client.Common.Interfaces;
using Theia.App.Client.Common.Services.AppState;
using Theia.App.Client.Common.Services.Breadcrumbs;
using Theia.App.Client.Common.Services.Http;
using Theia.App.Client.Common.Services.Tab;
using Theia.App.Client.Common.Services.UI;

namespace Theia.App.Client.Common;

public static class DependencyInjection
{
    public static void AddCommon(this WebAssemblyHostBuilder builder, CommonOptions options)
    {
        builder.Services.AddScoped<IBreadcrumbService, BreadcrumbService>();
        builder.Services.AddScoped<ITabService, TabService>();
        builder.Services.AddSingleton(typeof(IDrawerItemsProvider), options.DrawerItemsProviderType);
        builder.Services.AddSingleton(options);
        builder.Services.AddSingleton<IAppStateManager, AppStateManager>();

        builder.Services.AddScoped<SpinnerService>();
        builder.Services.AddScoped<ICommonClient, CommonClient>();
        builder.Services.AddScoped<ISubmissionQuestionsClient, SubmissionQuestionsClient>();
        builder.Services.AddScoped<ISubmissionFilesClient, SubmissionFilesClient>();
        builder.Services.AddScoped<ISubmissionsClient, SubmissionsClient>();
        builder.Services.AddScoped<IIndustriesClient, IndustriesClient>();
        builder.Services.AddScoped<ICountriesClient, CountriesClient>();
        builder.Services.AddScoped<IContractsClient, ContractsClient>();
        builder.Services.AddScoped<ICountriesRevenueClient, CountriesRevenueClient>();
        builder.Services.AddScoped<IIndustriesRevenueClient, IndustriesRevenueClient>();
        builder.Services.AddScoped<IInfrastructureClient, InfrastructureClient>();
        builder.Services.AddScoped<IDataCentreLocationsClient, DataCentreLocationsClient>();
        builder.Services.AddScoped<VendorsClient>();
        builder.Services.AddScoped<StoredDataClient>();
        builder.Services.AddScoped<SupplierServicesClient>();
        builder.Services.AddScoped<PolicyFormsCommonClient>();
        builder.Services.AddScoped<LayersClient>();
        builder.Services.AddScoped<IndicationRequestsClient>();
        builder.Services.AddScoped<IndicationsClient>();
        builder.Services.AddScoped<QuotaSharesClient>();
        builder.Services.AddScoped<WholesaleClient>();
        
        builder.Services.AddTransient<CustomAuthorizationMessageHandler>();
        builder.Services.AddTransient<TenantHeaderMessageHandler>();
        
        Uri baseAddress =
                    new(builder.Configuration.GetSection("BaseApiUrl").Value
                        ?? throw new InvalidOperationException(
                            "TenantMode section is missing from the appsettings.json file."));

        builder.Services
            .AddHttpClient<HttpService>(client => client.BaseAddress = baseAddress)
            .AddHttpMessageHandler<CustomAuthorizationMessageHandler>()
            .AddHttpMessageHandler<TenantHeaderMessageHandler>();

        builder.Services
            .AddHttpClient<AnonymousHttpService>(client => client.BaseAddress = baseAddress)
            .AddHttpMessageHandler<TenantHeaderMessageHandler>();

        builder.Services.AddOidcAuthentication(options =>
        {
            builder.Configuration.Bind("Auth0", options.ProviderOptions);
            options.ProviderOptions.ResponseType = "code";
            options.ProviderOptions.AdditionalProviderParameters.Add("audience",  builder.Configuration["Auth0:Audience"] ?? throw new InvalidOperationException("Missing config"));
            options.ProviderOptions.AdditionalProviderParameters.Add("organization",  builder.Configuration["Auth0:Organisation"] ?? throw new InvalidOperationException("Missing config"));
        });

        builder.Services.AddTelerikBlazor();
        builder.Services.AddBlazoredLocalStorage();

        builder.Services.AddScoped<MainLayoutState>();
        
        builder.Services.AddFluxor(o => o.ScanAssemblies(Assembly.GetExecutingAssembly()));
    }
}

public record CommonOptions(Type DrawerItemsProviderType, bool ShowAccountManagement = false);