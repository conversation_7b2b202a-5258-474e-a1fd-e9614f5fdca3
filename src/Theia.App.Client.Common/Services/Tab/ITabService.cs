namespace Theia.App.Client.Common.Services.Tab;

public interface ITabService
{
    Task SetActiveTabAsync(string tabName, bool addToHistory);
    Task SetActiveTabAsync(string tabName, string? context, bool addToHistory);
    Task SetNestedTabAsync(string parentTab, string childTab, bool addToHistory);
    Task SetNestedTabAsync(string parentTab, string? parentContext, string childTab, string? childContext, bool addToHistory);

    string GetActiveTabFromUrl();
    string GetActiveTabFromUrl(string? context);
    TabInfo GetTabInfoFromUrl();
    TabInfo[] GetAllTabsFromUrl();

    string NormalizeTabName(string tabName);
}

public record TabInfo(string TabName, string? Context, int Level);

public record TabHierarchy(TabInfo[] Tabs)
{
    public TabInfo? GetTabByContext(string? context) =>
        Tabs.FirstOrDefault(t => string.Equals(t.Context, context, StringComparison.OrdinalIgnoreCase));

    public TabInfo? GetTabByLevel(int level) =>
        Tabs.FirstOrDefault(t => t.Level == level);

    public string[] GetTabNames() => Tabs.Select(t => t.TabName).ToArray();
}