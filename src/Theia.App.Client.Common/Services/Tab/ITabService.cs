namespace Theia.App.Client.Common.Services.Tab;

/// <summary>
/// Represents information about a tab in the URL hierarchy
/// </summary>
/// <param name="TabName">The name of the tab</param>
/// <param name="Context">The context this tab belongs to (e.g., "layers", "application-forms"), or null if no context</param>
/// <param name="Level">The hierarchical level of this tab (0-based)</param>
public record TabInfo(string TabName, string? Context, int Level);

/// <summary>
/// Represents a hierarchy of tabs for easier navigation and querying
/// </summary>
/// <param name="Tabs">Array of tabs in hierarchical order</param>
public record TabHierarchy(TabInfo[] Tabs)
{
    /// <summary>
    /// Gets a tab by its context
    /// </summary>
    public TabInfo? GetTabByContext(string context) =>
        Tabs.FirstOrDefault(t => string.Equals(t.Context, context, StringComparison.OrdinalIgnoreCase));

    /// <summary>
    /// Gets all tab names in order
    /// </summary>
    public string[] GetTabNames() => Tabs.Select(t => t.TabName).ToArray();

    /// <summary>
    /// Gets the deepest (last) tab in the hierarchy
    /// </summary>
    public TabInfo? GetDeepestTab() => Tabs.Length > 0 ? Tabs[^1] : null;
}

public interface ITabService
{
    /// <summary>
    /// Sets the active tab using legacy behavior (no explicit context)
    /// </summary>
    Task SetActiveTabAsync(string tabName, bool addToHistory);

    /// <summary>
    /// Sets the active tab with an optional context
    /// </summary>
    Task SetActiveTabAsync(string tabName, string? context, bool addToHistory);

    /// <summary>
    /// Sets nested tabs without explicit contexts
    /// </summary>
    Task SetNestedTabAsync(string parentTab, string childTab, bool addToHistory);

    /// <summary>
    /// Sets nested tabs with explicit contexts for both parent and child
    /// </summary>
    Task SetNestedTabAsync(string parentTab, string? parentContext, string childTab, string? childContext, bool addToHistory);

    /// <summary>
    /// Gets the active tab name from the current URL (returns the last tab in hierarchy)
    /// </summary>
    string GetActiveTabFromUrl();

    /// <summary>
    /// Gets the active tab name for a specific context from the current URL
    /// </summary>
    string GetActiveTabFromUrl(string? context);

    /// <summary>
    /// Gets detailed information about the current active tab
    /// </summary>
    TabInfo GetTabInfoFromUrl();

    /// <summary>
    /// Gets all tabs detected in the current URL with their contexts and hierarchy levels
    /// </summary>
    TabInfo[] GetAllTabsFromUrl();

    /// <summary>
    /// Normalizes a tab name for URL usage. Returns null if the input is null or empty.
    /// </summary>
    string? NormalizeTabName(string? tabName);
}