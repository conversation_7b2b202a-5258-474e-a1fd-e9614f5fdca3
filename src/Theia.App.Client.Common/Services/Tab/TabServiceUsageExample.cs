using Microsoft.AspNetCore.Components;

namespace Theia.App.Client.Common.Services.Tab;

/// <summary>
/// Example component demonstrating how to use the enhanced TabService
/// </summary>
public partial class TabServiceUsageExample : ComponentBase
{
    [Inject] private ITabService TabService { get; set; } = null!;

    protected override async Task OnInitializedAsync()
    {
        // Example 1: Simple tab setting (legacy behavior)
        await TabService.SetActiveTabAsync("Layers", true);
        // URL becomes: /submission/Layers

        // Example 2: Tab with explicit context
        await TabService.SetActiveTabAsync("Layer1", "layers", true);
        // URL becomes: /submission/layers/Layer1

        // Example 3: Nested tabs without contexts
        await TabService.SetNestedTabAsync("Layer1", "Form1", true);
        // URL becomes: /submission/Layer1/Form1

        // Example 4: Nested tabs with contexts
        await TabService.SetNestedTabAsync("Layer1", "layers", "Form1", "application-forms", true);
        // URL becomes: /submission/layers/Layer1/application-forms/Form1

        // Example 5: Reading tab information
        string activeTab = TabService.GetActiveTabFromUrl();
        // Returns the last tab in the hierarchy

        string layerTab = TabService.GetActiveTabFromUrl("layers");
        // Returns the tab for the "layers" context

        TabInfo tabInfo = TabService.GetTabInfoFromUrl();
        // Returns detailed information about the last tab

        TabInfo[] allTabs = TabService.GetAllTabsFromUrl();
        // Returns all detected tabs with their contexts and levels

        // Example 6: Working with tab hierarchy
        TabHierarchy hierarchy = new(allTabs);
        TabInfo? layerTabInfo = hierarchy.GetTabByContext("layers");
        TabInfo? formTabInfo = hierarchy.GetTabByContext("application-forms");
        string[] tabNames = hierarchy.GetTabNames();
    }

    // Example usage in a tab component
    private async Task OnTabChanged(string tabName)
    {
        // For a simple tab change
        await TabService.SetActiveTabAsync(tabName, true);
    }

    private async Task OnLayerTabChanged(string layerName)
    {
        // For a layer tab with context
        await TabService.SetActiveTabAsync(layerName, "layers", true);
    }

    private async Task OnNestedTabChanged(string parentTab, string childTab)
    {
        // For nested tabs with contexts
        await TabService.SetNestedTabAsync(parentTab, "layers", childTab, "application-forms", true);
    }

    // Example of reading current tab state
    private void AnalyzeCurrentUrl()
    {
        TabInfo[] tabs = TabService.GetAllTabsFromUrl();
        
        foreach (TabInfo tab in tabs)
        {
            Console.WriteLine($"Level {tab.Level}: {tab.TabName} (Context: {tab.Context ?? "None"})");
        }

        // Example output for URL: /submission/layers/Layer1/application-forms/Form1
        // Level 0: Layer1 (Context: layers)
        // Level 1: Form1 (Context: application-forms)
    }
}
