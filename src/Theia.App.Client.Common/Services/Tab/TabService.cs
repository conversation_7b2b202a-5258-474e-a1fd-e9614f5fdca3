using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Theia.App.Client.Common.Services.Tab;

public class TabService(NavigationManager navigationManager, IJSRuntime jsRuntime) : ITabService
{
    // Common tab contexts that we recognize - this is not exhaustive
    // Used for intelligent context detection, but we don't require all contexts to be known
    private static readonly Dictionary<string, int> CommonTabContexts = new(StringComparer.OrdinalIgnoreCase)
    {
        { "layers", 0 },
        { "application-forms", 1 },
        { "endorsements", 1 },
        { "subjectivities", 1 },
        { "documents", 1 },
        { "financials", 1 },
        { "vendors", 1 },
        { "contracts", 1 }
    };

    public async Task SetActiveTabAsync(string tabName, bool addToHistory)
    {
        string? normalizedTab = NormalizeTabName(tabName);
        if (string.IsNullOrEmpty(normalizedTab))
            return;

        string basePath = GetBasePath();
        string newUrl = $"{basePath}/{normalizedTab}";

        string stateName = addToHistory ? "pushState" : "replaceState";
        await jsRuntime.InvokeVoidAsync($"history.{stateName}", null, "", newUrl);
    }

    public async Task SetActiveTabAsync(string tabName, string? context, bool addToHistory)
    {
        string? normalizedTab = NormalizeTabName(tabName);
        if (string.IsNullOrEmpty(normalizedTab))
            return;

        string basePath = GetBasePath(context);
        string newUrl = context != null ? $"{basePath}/{context}/{normalizedTab}" : $"{basePath}/{normalizedTab}";

        string stateName = addToHistory ? "pushState" : "replaceState";
        await jsRuntime.InvokeVoidAsync($"history.{stateName}", null, "", newUrl);
    }

    public async Task SetNestedTabAsync(string parentTab, string childTab, bool addToHistory)
    {
        string? normalizedParent = NormalizeTabName(parentTab);
        string? normalizedChild = NormalizeTabName(childTab);

        if (string.IsNullOrEmpty(normalizedParent) || string.IsNullOrEmpty(normalizedChild))
            return;

        string basePath = GetBasePath();
        string newUrl = $"{basePath}/{normalizedParent}/{normalizedChild}";

        string stateName = addToHistory ? "pushState" : "replaceState";
        await jsRuntime.InvokeVoidAsync($"history.{stateName}", null, "", newUrl);
    }

    public async Task SetNestedTabAsync(string parentTab, string? parentContext, string childTab, string? childContext, bool addToHistory)
    {
        string? normalizedParent = NormalizeTabName(parentTab);
        string? normalizedChild = NormalizeTabName(childTab);

        if (string.IsNullOrEmpty(normalizedParent) || string.IsNullOrEmpty(normalizedChild))
            return;

        string basePath = GetBasePath();

        // Build URL with contexts if provided
        List<string> urlParts = [basePath];

        if (!string.IsNullOrEmpty(parentContext))
            urlParts.Add(parentContext);
        urlParts.Add(normalizedParent);

        if (!string.IsNullOrEmpty(childContext))
            urlParts.Add(childContext);
        urlParts.Add(normalizedChild);

        string newUrl = string.Join("/", urlParts);

        string stateName = addToHistory ? "pushState" : "replaceState";
        await jsRuntime.InvokeVoidAsync($"history.{stateName}", null, "", newUrl);
    }

    public string GetActiveTabFromUrl()
    {
        Uri currentUri = new(navigationManager.Uri);
        string[] segments = currentUri.AbsolutePath.Split('/', StringSplitOptions.RemoveEmptyEntries);

        return segments.Length > 0 ? segments[^1] : string.Empty;
    }

    public string GetActiveTabFromUrl(string? context)
    {
        if (string.IsNullOrEmpty(context))
            return GetActiveTabFromUrl();

        TabInfo[] tabs = GetAllTabsFromUrl();
        TabInfo? contextTab = tabs.FirstOrDefault(t => string.Equals(t.Context, context, StringComparison.OrdinalIgnoreCase));

        return contextTab?.TabName ?? string.Empty;
    }

    public TabInfo GetTabInfoFromUrl()
    {
        TabInfo[] allTabs = GetAllTabsFromUrl();
        return allTabs.Length > 0 ? allTabs[^1] : new TabInfo(string.Empty, null, 0);
    }

    public TabInfo[] GetAllTabsFromUrl()
    {
        Uri currentUri = new(navigationManager.Uri);
        string[] segments = currentUri.AbsolutePath.Split('/', StringSplitOptions.RemoveEmptyEntries);

        if (segments.Length == 0)
            return [];

        List<TabInfo> tabs = [];
        int level = 0;

        for (int i = 0; i < segments.Length; i++)
        {
            string segment = segments[i];

            // Skip base segments that are not tabs (like "submission")
            if (IsBaseSegment(segment))
                continue;

            // Check if this segment is a known context
            if (IsKnownContext(segment))
            {
                // Next segment should be the tab name for this context
                if (i + 1 < segments.Length)
                {
                    string tabName = segments[i + 1];
                    if (!IsKnownContext(tabName) && !IsBaseSegment(tabName))
                    {
                        tabs.Add(new TabInfo(tabName, segment, level));
                        level++;
                        i++; // Skip the tab name segment since we've processed it
                    }
                }
            }
            else if (!IsBaseSegment(segment))
            {
                // This is likely a tab without an explicit context
                // Try to infer context from position or use null
                string? inferredContext = InferContextFromPosition(i, segments);
                tabs.Add(new TabInfo(segment, inferredContext, level));
                level++;
            }
        }

        return [.. tabs];
    }

    public string? NormalizeTabName(string? tabName)
    {
        if (string.IsNullOrWhiteSpace(tabName))
            return null;

        return tabName.Replace(" ", "").Trim();
    }

    private string GetBasePath(string? context = null)
    {
        Uri currentUri = new(navigationManager.Uri);
        string[] segments = currentUri.AbsolutePath.Split('/', StringSplitOptions.RemoveEmptyEntries);

        if (segments.Length == 0)
            return "/";

        // If we have a specific context, remove segments from that context onwards
        if (!string.IsNullOrEmpty(context))
        {
            for (int i = 0; i < segments.Length; i++)
            {
                if (string.Equals(segments[i], context, StringComparison.OrdinalIgnoreCase))
                {
                    string[] contextBaseSegments = segments[..i];
                    return "/" + string.Join("/", contextBaseSegments);
                }
            }
        }

        // Remove tab-related segments from the end
        List<string> baseSegments = [];

        for (int i = 0; i < segments.Length; i++)
        {
            string segment = segments[i];

            // Keep base segments (like "submission")
            if (IsBaseSegment(segment))
            {
                baseSegments.Add(segment);
                continue;
            }

            // If this is a known context, we might want to keep it depending on the situation
            if (IsKnownContext(segment))
            {
                // Don't include this context or anything after it in the base path
                break;
            }

            // If this looks like a tab name, don't include it or anything after
            if (IsLikelyTabSegment(segment))
            {
                break;
            }

            // Otherwise, include this segment
            baseSegments.Add(segment);
        }

        return "/" + string.Join("/", baseSegments);
    }

    private bool IsBaseSegment(string segment)
    {
        // Common base segments that are not tabs or contexts
        string[] baseSegments = ["submission", "admin", "manage", "account"];
        return baseSegments.Contains(segment, StringComparer.OrdinalIgnoreCase);
    }

    private bool IsKnownContext(string segment)
    {
        return CommonTabContexts.ContainsKey(segment);
    }

    private string? InferContextFromPosition(int position, string[] segments)
    {
        // Try to infer context based on position and surrounding segments
        // This is a best-effort approach for unknown contexts

        // Look for a context segment before this position
        for (int i = position - 1; i >= 0; i--)
        {
            if (IsKnownContext(segments[i]))
            {
                return segments[i];
            }
        }

        // If we can't infer a context, return null
        return null;
    }

    private bool IsLikelyTabSegment(string segment)
    {
        if (string.IsNullOrEmpty(segment))
            return false;

        // Don't treat known contexts as tab segments
        if (IsKnownContext(segment))
            return false;

        // Don't treat base segments as tab segments
        if (IsBaseSegment(segment))
            return false;

        // Common patterns for tab names (no spaces, CamelCase, etc.)
        // But be more permissive since we don't know all possible tab formats
        return segment.Length > 1 &&
               !segment.Contains(' ') &&
               !segment.Contains('.') && // Avoid file extensions
               !segment.All(char.IsDigit); // Avoid pure numbers (likely IDs)
    }

    private string BuildNestedTabUrl(string basePath, string parentTab, string? parentContext, string childTab, string? childContext)
    {
        List<string> parts = [basePath];

        if (!string.IsNullOrEmpty(parentContext))
            parts.Add(parentContext);
        parts.Add(parentTab);

        if (!string.IsNullOrEmpty(childContext))
            parts.Add(childContext);
        parts.Add(childTab);

        return string.Join("/", parts);
    }
}