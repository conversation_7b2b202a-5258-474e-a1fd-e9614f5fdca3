using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Theia.App.Client.Common.Services.Tab;

public class TabService(NavigationManager navigationManager, IJSRuntime jsRuntime) : ITabService
{
    private static readonly Dictionary<string, string> KnownTabContexts = new(StringComparer.OrdinalIgnoreCase)
    {
        { "layers", "layers" },
        { "application-forms", "application-forms" },
        { "forms", "application-forms" },
        { "endorsements", "endorsements" },
        { "subjectivities", "subjectivities" },
        { "quota-shares", "quota-shares" },
        { "indications", "indications" },
        { "submissions", "submissions" }
    };

    public async Task SetActiveTabAsync(string tabName, bool addToHistory)
    {
        await SetActiveTabAsync(tabName, null, addToHistory);
    }

    public async Task SetActiveTabAsync(string tabName, string? context, bool addToHistory)
    {
        string normalizedTab = NormalizeTabName(tabName);
        string basePath = GetBasePath(context);
        string newUrl = context != null ? $"{basePath}/{context}/{normalizedTab}" : $"{basePath}/{normalizedTab}";

        string stateName = addToHistory ? "pushState" : "replaceState";
        await jsRuntime.InvokeVoidAsync($"history.{stateName}", null, "", newUrl);
    }

    public async Task SetNestedTabAsync(string parentTab, string childTab, bool addToHistory)
    {
        await SetNestedTabAsync(parentTab, null, childTab, null, addToHistory);
    }

    public async Task SetNestedTabAsync(string parentTab, string? parentContext, string childTab, string? childContext, bool addToHistory)
    {
        string normalizedParentTab = NormalizeTabName(parentTab);
        string normalizedChildTab = NormalizeTabName(childTab);
        string basePath = GetBasePath(null, 2); // Remove up to 2 tab segments

        string newUrl = BuildNestedTabUrl(basePath, normalizedParentTab, parentContext, normalizedChildTab, childContext);

        string stateName = addToHistory ? "pushState" : "replaceState";
        await jsRuntime.InvokeVoidAsync($"history.{stateName}", null, "", newUrl);
    }

    public string GetActiveTabFromUrl()
    {
        TabInfo[] tabs = GetAllTabsFromUrl();
        return tabs.Length > 0 ? tabs[^1].TabName : string.Empty;
    }

    public string GetActiveTabFromUrl(string? context)
    {
        TabInfo[] tabs = GetAllTabsFromUrl();
        TabInfo? tab = tabs.FirstOrDefault(t => string.Equals(t.Context, context, StringComparison.OrdinalIgnoreCase));
        return tab?.TabName ?? string.Empty;
    }

    public TabInfo GetTabInfoFromUrl()
    {
        TabInfo[] tabs = GetAllTabsFromUrl();
        return tabs.Length > 0 ? tabs[^1] : new TabInfo(string.Empty, null, 0);
    }

    public TabInfo[] GetAllTabsFromUrl()
    {
        Uri currentUri = new(navigationManager.Uri);
        string[] segments = currentUri.AbsolutePath.Split('/', StringSplitOptions.RemoveEmptyEntries);

        List<TabInfo> tabs = new();
        int level = 0;

        for (int i = 0; i < segments.Length; i++)
        {
            string segment = segments[i];

            // Check if this segment is a known context
            if (IsKnownContext(segment))
            {
                // Next segment should be a tab for this context
                if (i + 1 < segments.Length && IsLikelyTabSegment(segments[i + 1]))
                {
                    tabs.Add(new TabInfo(segments[i + 1], segment, level));
                    i++; // Skip the tab segment as we've processed it
                    level++;
                }
            }
            // Check if this segment is a likely tab without explicit context
            else if (IsLikelyTabSegment(segment))
            {
                // Try to infer context from previous segment or position
                string? inferredContext = InferContextFromPosition(segments, i);
                tabs.Add(new TabInfo(segment, inferredContext, level));
                level++;
            }
        }

        return tabs.ToArray();
    }

    public string NormalizeTabName(string tabName) =>
        tabName.Replace(" ", "").Trim();

    private string GetBasePath(string? context = null, int segmentsToRemove = 1)
    {
        Uri currentUri = new(navigationManager.Uri);
        string[] segments = currentUri.AbsolutePath.Split('/', StringSplitOptions.RemoveEmptyEntries);

        if (segments.Length == 0)
            return "/";

        // If we have a specific context, try to find and remove from that point
        if (context != null)
        {
            int contextIndex = Array.FindIndex(segments, s => string.Equals(s, context, StringComparison.OrdinalIgnoreCase));
            if (contextIndex >= 0)
            {
                string[] contextBaseSegments = segments[..contextIndex];
                return "/" + string.Join("/", contextBaseSegments);
            }
        }

        // Remove segments from the end based on segmentsToRemove parameter
        int segmentsToKeep = Math.Max(0, segments.Length - segmentsToRemove);

        // Look for tab segments to remove from the end
        int actualSegmentsToRemove = 0;
        for (int i = segments.Length - 1; i >= 0 && actualSegmentsToRemove < segmentsToRemove; i--)
        {
            string segment = segments[i];

            // If it's a likely tab segment or known context, remove it
            if (IsLikelyTabSegment(segment) || IsKnownContext(segment))
            {
                actualSegmentsToRemove++;
            }
            else
            {
                break; // Stop if we hit a non-tab segment
            }
        }

        segmentsToKeep = Math.Max(0, segments.Length - actualSegmentsToRemove);
        string[] baseSegments = segments[..segmentsToKeep];
        return "/" + string.Join("/", baseSegments);
    }

    private string BuildNestedTabUrl(string basePath, string parentTab, string? parentContext, string childTab, string? childContext)
    {
        List<string> urlParts = new() { basePath.TrimEnd('/') };

        // Add parent tab with context if specified
        if (parentContext != null)
        {
            urlParts.Add(parentContext);
        }
        urlParts.Add(parentTab);

        // Add child tab with context if specified
        if (childContext != null)
        {
            urlParts.Add(childContext);
        }
        urlParts.Add(childTab);

        return string.Join("/", urlParts.Where(p => !string.IsNullOrEmpty(p)));
    }

    private bool IsLikelyTabSegment(string segment)
    {
        if (string.IsNullOrWhiteSpace(segment) || segment.Length <= 2)
            return false;

        // Common patterns for tab names (CamelCase, no spaces, starts with uppercase)
        return !segment.Contains('-') &&
               !segment.Contains('_') &&
               !segment.Contains(' ') &&
               char.IsUpper(segment[0]) &&
               segment.All(c => char.IsLetterOrDigit(c));
    }

    private bool IsKnownContext(string segment)
    {
        return KnownTabContexts.ContainsKey(segment);
    }

    private string? InferContextFromPosition(string[] segments, int currentIndex)
    {
        // Look at the previous segment to see if it's a known context
        if (currentIndex > 0 && IsKnownContext(segments[currentIndex - 1]))
        {
            return segments[currentIndex - 1];
        }

        // Look for patterns in the URL structure to infer context
        // For example, if we see "submission" followed by a tab, it might be a layer
        if (currentIndex > 0)
        {
            string previousSegment = segments[currentIndex - 1].ToLowerInvariant();
            return previousSegment switch
            {
                "submission" => "layers",
                "submissions" => "layers",
                _ => null
            };
        }

        return null;
    }
}