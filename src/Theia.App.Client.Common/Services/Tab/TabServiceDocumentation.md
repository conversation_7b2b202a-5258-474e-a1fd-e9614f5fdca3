# Enhanced TabService Documentation

## Overview

The enhanced TabService now supports flexible nested tabs and multi-level URL tracking with improved handling of unknown contexts and null values.

## Key Improvements

### 1. **Flexible Context Handling**
- **No longer requires all contexts to be predefined**
- **Unknown contexts are fully supported**
- **Graceful handling of null/empty values**

### 2. **Robust Null Safety**
- **NormalizeTabName returns `string?` and handles null inputs**
- **All methods check for null/empty inputs before processing**
- **Safe fallbacks for missing tab information**

### 3. **Intelligent Tab Detection**
- **Uses common contexts for hints, but doesn't require them**
- **Flexible URL parsing that adapts to different structures**
- **Better handling of edge cases and malformed URLs**

## Usage Examples

### Basic Tab Navigation

```csharp
// Simple tab (legacy behavior)
await TabService.SetActiveTabAsync("Layers", true);
// URL: /submission/Layers

// Tab with known context
await TabService.SetActiveTabAsync("Layer1", "layers", true);
// URL: /submission/layers/Layer1

// Tab with unknown context - still works!
await TabService.SetActiveTabAsync("CustomTab", "my-custom-context", true);
// URL: /submission/my-custom-context/CustomTab
```

### Nested Tab Navigation

```csharp
// Simple nested tabs
await TabService.SetNestedTabAsync("Layer1", "Form1", true);
// URL: /submission/Layer1/Form1

// Nested tabs with known contexts
await TabService.SetNestedTabAsync("Layer1", "layers", "Form1", "application-forms", true);
// URL: /submission/layers/Layer1/application-forms/Form1

// Nested tabs with unknown contexts - fully supported!
await TabService.SetNestedTabAsync("Parent", "unknown-parent", "Child", "unknown-child", true);
// URL: /submission/unknown-parent/Parent/unknown-child/Child
```

### Reading Tab Information

```csharp
// Get the active tab (last in hierarchy)
string activeTab = TabService.GetActiveTabFromUrl();
// Returns: tab name or empty string if no tabs

// Get tab for specific context
string layerTab = TabService.GetActiveTabFromUrl("layers");
// Returns: tab name or empty string if context not found

// Get detailed tab information
TabInfo tabInfo = TabService.GetTabInfoFromUrl();
// Returns: TabInfo with details or empty TabInfo if no tabs

// Get all tabs in hierarchy
TabInfo[] allTabs = TabService.GetAllTabsFromUrl();
// Returns: array of all detected tabs (empty array if none)
```

### Working with Tab Hierarchy

```csharp
TabInfo[] allTabs = TabService.GetAllTabsFromUrl();
TabHierarchy hierarchy = new(allTabs);

// Find tabs by context (works with any context, known or unknown)
TabInfo? layerTab = hierarchy.GetTabByContext("layers");
TabInfo? customTab = hierarchy.GetTabByContext("my-custom-context");

// Get all tab names
string[] tabNames = hierarchy.GetTabNames();

// Get the deepest tab
TabInfo? deepestTab = hierarchy.GetDeepestTab();
```

## Error Handling

The service now gracefully handles various edge cases:

```csharp
// Null or empty tab names are safely ignored
await TabService.SetActiveTabAsync(null, true); // Does nothing
await TabService.SetActiveTabAsync("", true);   // Does nothing
await TabService.SetActiveTabAsync("  ", true); // Does nothing

// Unknown contexts are fully supported
await TabService.SetActiveTabAsync("Tab", "completely-unknown-context", true);
// Works perfectly - creates: /submission/completely-unknown-context/Tab

// Reading from URLs without tabs
string tab = TabService.GetActiveTabFromUrl(); // Returns empty string
TabInfo[] tabs = TabService.GetAllTabsFromUrl(); // Returns empty array
```

## Backward Compatibility

All existing code continues to work unchanged:

```csharp
// Legacy usage still works exactly as before
await TabService.SetActiveTabAsync("Layers", true);
string activeTab = TabService.GetActiveTabFromUrl();
string normalizedTab = TabService.NormalizeTabName("My Tab"); // Now returns string?
```

## Common Tab Contexts

The service recognizes these common contexts for intelligent parsing, but **does not require them**:

- `layers`
- `application-forms`
- `endorsements`
- `subjectivities`
- `documents`
- `financials`
- `vendors`
- `contracts`

**Important**: You can use any context name you want - the service will handle it correctly even if it's not in this list.

## URL Examples

The service can handle various URL structures:

```
/submission/Layers                                    # Simple tab
/submission/layers/Layer1                             # Tab with context
/submission/layers/Layer1/application-forms/Form1    # Nested with contexts
/submission/custom-context/CustomTab                  # Unknown context
/submission/Parent/Child                              # Simple nested
/submission/unknown/Parent/another-unknown/Child     # Multiple unknown contexts
```

## Migration Guide

### From Basic TabService

No changes needed - all existing code works as-is.

### Adding Context Support

```csharp
// Before
await TabService.SetActiveTabAsync("Layer1", true);

// After - add context for better URL structure
await TabService.SetActiveTabAsync("Layer1", "layers", true);
```

### Adding Nested Tab Support

```csharp
// Before - separate calls
await TabService.SetActiveTabAsync("Layer1", true);
// ... later ...
await TabService.SetActiveTabAsync("Form1", true);

// After - single call for nested structure
await TabService.SetNestedTabAsync("Layer1", "layers", "Form1", "application-forms", true);
```
