# TabService Enhanced Features

## Overview
The enhanced TabService now supports nested tabs and multiple tab contexts, allowing for complex URL structures like:
- `mydomain.com/submission/Layers/Layer1`
- `mydomain.com/submission/Layers/Layer1/application-forms/Form1`

## New Features

### 1. Context-Aware Tab Setting
```csharp
// Set a tab with explicit context
await tabService.SetActiveTabAsync("Layer1", "layers", true);
// Results in: /submission/layers/Layer1

// Set a tab without context (legacy behavior)
await tabService.SetActiveTabAsync("Layer1", true);
// Results in: /submission/Layer1
```

### 2. Nested Tab Support
```csharp
// Set nested tabs without contexts
await tabService.SetNestedTabAsync("Layer1", "Form1", true);
// Results in: /submission/Layer1/Form1

// Set nested tabs with contexts
await tabService.SetNestedTabAsync("Layer1", "layers", "Form1", "application-forms", true);
// Results in: /submission/layers/Layer1/application-forms/Form1
```

### 3. Enhanced Tab Retrieval
```csharp
// Get the active tab (last tab in hierarchy)
string activeTab = tabService.GetActiveTabFromUrl();

// Get active tab for specific context
string layerTab = tabService.GetActiveTabFromUrl("layers");
string formTab = tabService.GetActiveTabFromUrl("application-forms");

// Get detailed tab information
TabInfo tabInfo = tabService.GetTabInfoFromUrl();
// Returns: TabInfo with TabName, Context, and Level

// Get all tabs in the URL
TabInfo[] allTabs = tabService.GetAllTabsFromUrl();
// Returns array of all detected tabs with their contexts and levels
```

### 4. Tab Hierarchy Support
```csharp
TabInfo[] tabs = tabService.GetAllTabsFromUrl();
TabHierarchy hierarchy = new(tabs);

// Get tab by context
TabInfo? layerTab = hierarchy.GetTabByContext("layers");
TabInfo? formTab = hierarchy.GetTabByContext("application-forms");

// Get tab by level
TabInfo? firstLevelTab = hierarchy.GetTabByLevel(0);
TabInfo? secondLevelTab = hierarchy.GetTabByLevel(1);

// Get all tab names
string[] tabNames = hierarchy.GetTabNames();
```

## URL Examples

### Simple Tab (Legacy)
- URL: `mydomain.com/submission/Layers`
- Detected: `TabInfo("Layers", null, 0)`

### Context-Based Tab
- URL: `mydomain.com/submission/layers/Layer1`
- Detected: `TabInfo("Layer1", "layers", 0)`

### Nested Tabs
- URL: `mydomain.com/submission/layers/Layer1/application-forms/Form1`
- Detected: 
  - `TabInfo("Layer1", "layers", 0)`
  - `TabInfo("Form1", "application-forms", 1)`

### Mixed Context and Legacy
- URL: `mydomain.com/submission/Layers/application-forms/Form1`
- Detected:
  - `TabInfo("Layers", "layers", 0)` (inferred context)
  - `TabInfo("Form1", "application-forms", 1)`

## Known Tab Contexts
The service recognizes these contexts automatically:
- `layers`
- `application-forms` (also recognizes `forms` as alias)
- `endorsements`
- `subjectivities`
- `quota-shares`
- `indications`
- `submissions`

## Backward Compatibility
All existing functionality remains unchanged:
- `SetActiveTabAsync(string, bool)` works as before
- `GetActiveTabFromUrl()` returns the last tab in the hierarchy
- `NormalizeTabName(string)` works as before

The enhanced features are additive and don't break existing implementations.
