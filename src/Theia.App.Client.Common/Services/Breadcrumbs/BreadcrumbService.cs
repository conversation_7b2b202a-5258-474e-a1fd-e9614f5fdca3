using Fluxor;
using Microsoft.JSInterop;
using System.Text.Json;
using Theia.App.Client.Common.Extensions;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.State;
using Theia.FrontendResources;

namespace Theia.App.Client.Common.Services.Breadcrumbs;

public class BreadcrumbService(IJSRuntime jsRuntime, IDispatcher dispatcher) : IBreadcrumbService
{
    private const string BreadcrumbKey = "breadcrumbs";
    private const string DrawerItemKey = "draweritems";
    
    private List<BreadcrumbItem> BreadcrumbItems { get; set; } = [];

    public async Task AddBreadcrumb(BreadcrumbItem breadcrumbItem)
    {
        if (BreadcrumbItems is { Count: 0 })
        {
            BreadcrumbItems = await RetrieveBreadcrumbItemsFromSessionStorageAsync();

            // Is it still empty after retrieving it from the Session Storage?
            if (BreadcrumbItems is { Count: 0 })
            {
                BreadcrumbItems =
                [
                    new(Resource.Home, "/", true),
                    breadcrumbItem
                ];
            }
        }

        if (breadcrumbItem.IsMenuItem)
        {
            if (breadcrumbItem.Url is "/" or "home")
            {
                BreadcrumbItems = [breadcrumbItem];
            }
            else
            {
                BreadcrumbItems = [BreadcrumbItems[0], breadcrumbItem];
            }
        } else if (BreadcrumbItems.Any(x => x.Url == breadcrumbItem.Url))
        {
            int existingIndex = BreadcrumbItems.FindIndex(bc => bc.Url == breadcrumbItem.Url);
            if (existingIndex is not -1)
            {
                BreadcrumbItems = existingIndex is 0
                    ? [breadcrumbItem]
                    : BreadcrumbItems.Take(existingIndex + 1).ToList();
            }
        }
        else
        {
            BreadcrumbItems.Add(breadcrumbItem);
        }

        await RefreshAsync();
    }

    public async Task SetDrawerItem(DrawerItem newDrawerItem)
    {
        await jsRuntime.InvokeVoidAsync(
            "sessionStorage.setItem",
            DrawerItemKey,
            JsonSerializer.Serialize(newDrawerItem));
    }

    public async Task<DrawerItem?> RetrieveDrawerItem()
    {
        string drawerItemJson = await jsRuntime.InvokeAsync<string>("sessionStorage.getItem", DrawerItemKey);
        return string.IsNullOrWhiteSpace(drawerItemJson)
            ? null
            : JsonSerializer.Deserialize<DrawerItem>(drawerItemJson);
    }

    public async Task ReplaceLastItemAsync(BreadcrumbItem newItem)
    {
        BreadcrumbItems[^1] = newItem;
        await jsRuntime.ReplaceHistoryStateAsync(newItem.Url);
        await RefreshAsync();
    }

    private async Task RefreshAsync()
    {
        dispatcher.Dispatch(new ReplaceBreadcrumbsAction
        {
            BreadcrumbItems = BreadcrumbItems.ToArray()
        });
        
        await SendToSessionStorage();
    }

    private async Task SendToSessionStorage()
    {
        await jsRuntime.InvokeVoidAsync(
            "sessionStorage.setItem",
            BreadcrumbKey,
            JsonSerializer.Serialize(BreadcrumbItems));
    }

    private async Task<List<BreadcrumbItem>> RetrieveBreadcrumbItemsFromSessionStorageAsync()
    {
        string breadcrumbsJson = await jsRuntime.InvokeAsync<string>("sessionStorage.getItem", BreadcrumbKey);
        if (!string.IsNullOrWhiteSpace(breadcrumbsJson))
        {
            return JsonSerializer.Deserialize<List<BreadcrumbItem>>(breadcrumbsJson) ?? new();
        }

        return new();
    }
}