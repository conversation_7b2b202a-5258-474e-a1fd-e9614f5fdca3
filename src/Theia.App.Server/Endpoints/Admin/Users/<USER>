using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.App.Server.Services;
using Theia.Application.Services.TenantHostname;
using Theia.Domain.Entities.Identity;
using Theia.Domain.Entities.Settings;
using Theia.Identity.Interface;
using Theia.Identity.Interface.CreateUser;
using Theia.Identity.Interface.Shared;
using Theia.Infrastructure.Common.Constants;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;
using CreateUserResponse = Theia.Application.Features.Identity.Users.Commands.CreateUser.CreateUserResponse;

namespace Theia.App.Server.Endpoints.Admin.Users;

[ApiAuthorize(PermissionType = PermissionTypes.CreateUser)]
public class CreateUser(
        IApplicationDbContext dbContext,
        IAuthIdentityApi authIdentityApi,
        TimeProvider timeProvider,
        TenantHostnameService tenantHostnameService)
    : EndpointBaseAsync.WithRequest<CreateUserCommand>.WithResult<CreateUserResponse>
{
    [HttpPost("api/identity/Users/<USER>")]
    public override async Task<CreateUserResponse> HandleAsync(CreateUserCommand request,
        CancellationToken cancellationToken = new())
    {
        string trimmedEmail = request.Email.Trim();
        if (await dbContext.Users
                .AnyAsync(u => trimmedEmail == u.Email, cancellationToken)
                .ConfigureAwait(false))
        {
            throw new ApiProblemDetailsException(Resource.A_user_with_this_login_already_exists, HttpStatusCode.Conflict.ToInt());
        }

        var authRole =
            await dbContext.Roles
                .Where(x => x.Id == request.AssignedRoleId)
                .Select(x => new {x.AuthId, RoleId = x.Id, x.Name})
                .FirstOrDefaultAsync(cancellationToken)
                .ConfigureAwait(false);

        if (authRole is null)
        {
            throw new ApiProblemDetailsException(Resource.Role_not_found, StatusCodes.Status404NotFound);
        }
        
        Guid adminTenantId =
            await dbContext.Tenants
                .Where(x => x.Type == TenantType.Admin)
                .Select(x => x.Id)
                .SingleOrDefaultAsync(cancellationToken)
                .ConfigureAwait(false);
        
        Theia.Identity.Interface.CreateUser.CreateUserResponse response =
            await authIdentityApi.CreateUserAsync(
                new CreateUserRequest
                {
                    User = new CreateUserRequest.UserInfo
                    {
                        Email = request.Email, FirstName = request.Name, LastName = request.Surname
                    }, 
                    OrganisationTypes = OrganisationTypes.Admin,
                    RoleAuthId = authRole.AuthId,
                    TenantHostname = 
                        await tenantHostnameService
                            .BuildAsync(adminTenantId, cancellationToken)
                            .ConfigureAwait(false)
                },
                cancellationToken).ConfigureAwait(false);
            
        ApplicationUser user = new()
        {
            Id = response.AuthId,
            Name = request.Name,
            Surname = request.Surname,
            Email = trimmedEmail,
            PhoneNumber = request.PhoneNumber,
            Roles = [new UserRole
            {
                RoleId = authRole.RoleId,
                Role = null,
                User = null,
                UserId = null
            }]
        };

        dbContext.Users.Add(user);

        Guid? tenantId =
            await dbContext.Tenants
            .Where(x => x.Type == TenantType.Admin)
            .Select(x => (Guid?)x.Id)
            .SingleOrDefaultAsync(cancellationToken)
            .ConfigureAwait(false);

        if (!tenantId.HasValue)
        {
            throw new ApiProblemDetailsException(Resource.The_tenant_was_not_found, StatusCodes.Status404NotFound);
        }
        
        UserTenantControl tenantControl = new()
        {
            TenantId = tenantId.Value,
            Tenant = null,
            UserId = null,
            ApplicationUser = user
        };

        dbContext.UserTenantControls.Add(tenantControl);
        
        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(true);

        return new CreateUserResponse();
    }
}