<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup Label="Globals">
        <SccProjectName>SAK</SccProjectName>
        <SccProvider>SAK</SccProvider>
        <SccAuxPath>SAK</SccAuxPath>
        <SccLocalPath>SAK</SccLocalPath>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <LangVersion>12</LangVersion>
        <RunAnalyzersDuringBuild>true</RunAnalyzersDuringBuild>
        <UserSecretsId>bb4e7319-f24b-40a1-aee7-575912f7b1c6</UserSecretsId>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <DocumentationFile>wwwroot\api\Theia.App.Server.xml</DocumentationFile>
        <IsWebConfigTransformDisabled>true</IsWebConfigTransformDisabled>
    </PropertyGroup>

    <PropertyGroup>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Ardalis.ApiEndpoints" Version="4.1.0"/>
        <PackageReference Include="AutoWrapper.Core" Version="4.5.1" />
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
        <PackageReference Include="Hangfire.AspNetCore" Version="1.8.17" />
        <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.12" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.14">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.7" />
        <PackageReference Include="Nancy" Version="2.0.0" />
        <PackageReference Include="NSwag.MSBuild" Version="12.2.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Refit" Version="8.0.0" />
        <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
        <PackageReference Include="System.Text.Json" Version="8.0.5" />
        <PackageReference Include="Telerik.DataSource" Version="3.0.3" />
        <PackageReference Include="Theia.Http.Services" Version="1.0.4" />
    </ItemGroup>

    <ItemGroup>
        <None Include="wwwroot\api\Theia.WebAPI.xml" />
        <None Include="wwwroot\favicon.ico" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Theia.App.Jobs\Theia.App.Jobs.csproj" />
        <ProjectReference Include="..\Theia.App.Shared.Broking\Theia.App.Shared.Broking.csproj" />
        <ProjectReference Include="..\Theia.App.Shared.Login\Theia.App.Shared.Login.csproj" />
        <ProjectReference Include="..\Theia.App.Shared.Organisation\Theia.App.Shared.Organisation.csproj" />
        <ProjectReference Include="..\Theia.App.Shared.Suppliers\Theia.App.Shared.Suppliers.csproj" />
        <ProjectReference Include="..\Theia.App.Shared.Underwriting\Theia.App.Shared.Underwriting.csproj" />
        <ProjectReference Include="..\Theia.Application\Theia.Application.csproj" />
        <ProjectReference Include="..\Theia.Email.Contract\Theia.Email.Contract.csproj" />
        <ProjectReference Include="..\Theia.Email.SendGrid\Theia.Email.SendGrid.csproj" />
        <ProjectReference Include="..\Theia.Identity.Auth0\Theia.Identity.Auth0.csproj" />
        <ProjectReference Include="..\Theia.Identity.Interface\Theia.Identity.Interface.csproj" />
        <ProjectReference Include="..\Theia.Infrastructure\Theia.Infrastructure.csproj" />
        <ProjectReference Include="..\Theia.App.Shared.Admin\Theia.App.Shared.Admin.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="wwwroot\RequestedReports\Applicants\admin\" />
    </ItemGroup>

    <ItemGroup>
      <Content Update="wwwroot\api\Theia.App.Server.xml">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="Endpoints\Admin\Roles\CreateRole.cs" />
    </ItemGroup>

    <ItemGroup>
      <_ContentIncludedByDefault Remove="wwwroot\users\admindemo\admindemo-Avatar.PNG" />
      <_ContentIncludedByDefault Remove="wwwroot\users\admindemo\admindemo-ttt.jpg" />
    </ItemGroup>

    <ProjectExtensions>
        <VisualStudio>
            <UserProperties appsettings_1json__JsonSchema="" />
        </VisualStudio>
    </ProjectExtensions>

</Project>
