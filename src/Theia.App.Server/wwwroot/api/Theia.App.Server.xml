<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Theia.App.Server</name>
    </assembly>
    <members>
        <!-- Badly formed XML comment ignored for member "T:Theia.App.Server.Endpoints.Suppliers.Assessments.FinishAssessmentForSupplier" -->
        <!-- Badly formed XML comment ignored for member "M:Theia.App.Server.Endpoints.Suppliers.Assessments.FinishAssessmentForSupplier.#ctor(Theia.Application.Common.Interfaces.Persistence.IApplicationDbContext,Theia.Application.Common.Interfaces.Services.ITenantResolverService,Microsoft.AspNetCore.Http.IHttpContextAccessor,Theia.Application.Interfaces.ICommonServices,System.TimeProvider)" -->
        <member name="T:Refit.Implementation.Generated">
            <inheritdoc />
        </member>
        <member name="T:Refit.Implementation.Generated.TheiaAppServerServicesWebhooksIWebhookHttpService">
            <inheritdoc />
        </member>
        <member name="P:Refit.Implementation.Generated.TheiaAppServerServicesWebhooksIWebhookHttpService.Client">
            <inheritdoc />
        </member>
        <member name="M:Refit.Implementation.Generated.TheiaAppServerServicesWebhooksIWebhookHttpService.#ctor(System.Net.Http.HttpClient,Refit.IRequestBuilder)">
            <inheritdoc />
        </member>
        <member name="M:Refit.Implementation.Generated.TheiaAppServerServicesWebhooksIWebhookHttpService.PostCardAsync(Theia.App.Server.Services.Webhooks.WebhookCard,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Refit.Implementation.Generated.TheiaAppServerServicesWebhooksIWebhookHttpService.Theia#App#Server#Services#Webhooks#IWebhookHttpService#PostCardAsync(Theia.App.Server.Services.Webhooks.WebhookCard,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
    </members>
</doc>
