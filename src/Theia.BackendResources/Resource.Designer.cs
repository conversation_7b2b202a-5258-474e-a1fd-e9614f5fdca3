//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Theia.BackendResources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Theia.BackendResources.Resource", typeof(Resource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2FA is not configured.
        /// </summary>
        public static string _2FA_is_not_configured {
            get {
                return ResourceManager.GetString("2FA_is_not_configured", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A broking house user can be added only to a broking house.
        /// </summary>
        public static string A_broking_house_user_can_be_added_only_to_a_broking_house {
            get {
                return ResourceManager.GetString("A_broking_house_user_can_be_added_only_to_a_broking_house", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A head of cover with the same name already exists in the policy.
        /// </summary>
        public static string A_head_of_cover_with_the_same_name_already_exists_in_the_policy {
            get {
                return ResourceManager.GetString("A_head_of_cover_with_the_same_name_already_exists_in_the_policy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A policy form with the same name already exists.
        /// </summary>
        public static string A_policy_form_with_the_same_name_already_exists {
            get {
                return ResourceManager.GetString("A_policy_form_with_the_same_name_already_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A user with this login already exists..
        /// </summary>
        public static string A_user_with_this_login_already_exists {
            get {
                return ResourceManager.GetString("A_user_with_this_login_already_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access token timespan cannot be null..
        /// </summary>
        public static string Access_token_timespan_cannot_be_null {
            get {
                return ResourceManager.GetString("Access_token_timespan_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access token timespan is required..
        /// </summary>
        public static string Access_token_timespan_is_required {
            get {
                return ResourceManager.GetString("Access_token_timespan_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allowed username characters are required..
        /// </summary>
        public static string Allowed_username_characters_are_required {
            get {
                return ResourceManager.GetString("Allowed_username_characters_are_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An unknown failure has occurred..
        /// </summary>
        public static string An_unknown_failure_has_occurred {
            get {
                return ResourceManager.GetString("An_unknown_failure_has_occurred", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicant has been created successfully.
        /// </summary>
        public static string Applicant_has_been_created_successfully {
            get {
                return ResourceManager.GetString("Applicant_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicant has been deleted successfully..
        /// </summary>
        public static string Applicant_has_been_deleted_successfully {
            get {
                return ResourceManager.GetString("Applicant_has_been_deleted_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicant has been updated successfully..
        /// </summary>
        public static string Applicant_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("Applicant_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form answers could not be found.
        /// </summary>
        public static string Application_form_answer_not_found {
            get {
                return ResourceManager.GetString("Application_form_answer_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form code already exists.
        /// </summary>
        public static string Application_Form_code_already_exist {
            get {
                return ResourceManager.GetString("Application_Form_code_already_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application form has been created successfully.
        /// </summary>
        public static string Application_form_has_been_created_successfully {
            get {
                return ResourceManager.GetString("Application_form_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application form version already exists.
        /// </summary>
        public static string Application_form_version_already_exist {
            get {
                return ResourceManager.GetString("Application_form_version_already_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form Version data malformed.
        /// </summary>
        public static string Application_form_version_data_malformed {
            get {
                return ResourceManager.GetString("Application_form_version_data_malformed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application form version not found.
        /// </summary>
        public static string Application_form_version_not_found {
            get {
                return ResourceManager.GetString("Application_form_version_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application form with the same name and code already exists.
        /// </summary>
        public static string Application_form_with_the_same_name_and_code_already_exists {
            get {
                return ResourceManager.GetString("Application_form_with_the_same_name_and_code_already_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form Id is required.
        /// </summary>
        public static string ApplicationForm_Id_is_required {
            get {
                return ResourceManager.GetString("ApplicationForm_Id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form name already exist.
        /// </summary>
        public static string ApplicationForm_name_already_exist {
            get {
                return ResourceManager.GetString("ApplicationForm_name_already_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form name is required.
        /// </summary>
        public static string ApplicationForm_name_is_required {
            get {
                return ResourceManager.GetString("ApplicationForm_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form Page has been created successfully.
        /// </summary>
        public static string ApplicationForm_Page_has_been_created_successfully {
            get {
                return ResourceManager.GetString("ApplicationForm_Page_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form Page has been deleted successfully.
        /// </summary>
        public static string ApplicationForm_Page_has_been_deleted_successfully {
            get {
                return ResourceManager.GetString("ApplicationForm_Page_has_been_deleted_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form Page has been updated successfully.
        /// </summary>
        public static string ApplicationForm_Page_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("ApplicationForm_Page_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form page title is required.
        /// </summary>
        public static string ApplicationForm_page_title_is_required {
            get {
                return ResourceManager.GetString("ApplicationForm_page_title_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Association cannot be empty.
        /// </summary>
        public static string Association_can_not_be_null {
            get {
                return ResourceManager.GetString("Association_can_not_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom 50%.
        /// </summary>
        public static string Bottom_50_percent {
            get {
                return ResourceManager.GetString("Bottom_50_percent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brokers are invalid.
        /// </summary>
        public static string Brokers_are_invalid {
            get {
                return ResourceManager.GetString("Brokers_are_invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Broking houses unavailable for association were selected.
        /// </summary>
        public static string Broking_houses_unavailable_for_association_were_selected {
            get {
                return ResourceManager.GetString("Broking_houses_unavailable_for_association_were_selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can&apos;t create a new assessment because the supplier association is inactive.
        /// </summary>
        public static string Can_t_create_a_new_assessment_because_the_supplier_association_is_inactive {
            get {
                return ResourceManager.GetString("Can_t_create_a_new_assessment_because_the_supplier_association_is_inactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot access this tenant.
        /// </summary>
        public static string Cannot_access_tenant {
            get {
                return ResourceManager.GetString("Cannot_access_tenant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to bind user &apos;{0}&apos; to role &apos;{1}&apos;.
        /// </summary>
        public static string Cannot_bind_user_to_role {
            get {
                return ResourceManager.GetString("Cannot_bind_user_to_role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role &apos;{0}&apos; failed to create.
        /// </summary>
        public static string Cannot_create_roll {
            get {
                return ResourceManager.GetString("Cannot_create_roll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot disable Two-Factor Authentication for user with ID &apos;{0}&apos; as it&apos;s not currently enabled..
        /// </summary>
        public static string Cannot_disable_2FA_for_user_with_Id {
            get {
                return ResourceManager.GetString("Cannot_disable_2FA_for_user_with_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot edit a version in use.
        /// </summary>
        public static string Cannot_edit_a_version_in_use {
            get {
                return ResourceManager.GetString("Cannot_edit_a_version_in_use", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot generate recovery codes for username &apos;{0}&apos; as they do not have Two-Factor Authentication enabled..
        /// </summary>
        public static string Cannot_generate_recovery_codes {
            get {
                return ResourceManager.GetString("Cannot_generate_recovery_codes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot insert user &apos;{0}&apos;.
        /// </summary>
        public static string Cannot_insert_user {
            get {
                return ResourceManager.GetString("Cannot_insert_user", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot remove file.
        /// </summary>
        public static string Cannot_remove_file {
            get {
                return ResourceManager.GetString("Cannot_remove_file", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot submit already submitted form.
        /// </summary>
        public static string Cannot_submit_already_submitted_form {
            get {
                return ResourceManager.GetString("Cannot_submit_already_submitted_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can&apos;t send a submission to broker if it doesn&apos;t have any options selected.
        /// </summary>
        public static string Cant_share_wholesale_to_retail {
            get {
                return ResourceManager.GetString("Cant_share_wholesale_to_retail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choice has been created successfully.
        /// </summary>
        public static string Choice_has_been_created_successfully {
            get {
                return ResourceManager.GetString("Choice_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choice has been deleted successfully.
        /// </summary>
        public static string Choice_has_been_deleted_successfully {
            get {
                return ResourceManager.GetString("Choice_has_been_deleted_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choice has been updated successfully.
        /// </summary>
        public static string Choice_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("Choice_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choice is required.
        /// </summary>
        public static string Choice_is_required {
            get {
                return ResourceManager.GetString("Choice_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code is required..
        /// </summary>
        public static string Code_is_required {
            get {
                return ResourceManager.GetString("Code_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Password is required..
        /// </summary>
        public static string Confirm_password_is_required {
            get {
                return ResourceManager.GetString("Confirm_password_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm your email.
        /// </summary>
        public static string Confirm_your_email {
            get {
                return ResourceManager.GetString("Confirm_your_email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmation link to change email has been sent. Please check your email..
        /// </summary>
        public static string Confirmation_link_to_change_email_has_been_sent {
            get {
                return ResourceManager.GetString("Confirmation_link_to_change_email_has_been_sent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contract not found.
        /// </summary>
        public static string Contract_not_found {
            get {
                return ResourceManager.GetString("Contract_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework already exist.
        /// </summary>
        public static string Control_Framework_already_exist {
            get {
                return ResourceManager.GetString("Control_Framework_already_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occured during the analysis.
        /// </summary>
        public static string Control_Framework_analysis_error {
            get {
                return ResourceManager.GetString("Control_Framework_analysis_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Category already exist.
        /// </summary>
        public static string Control_Framework_Category_already_exist {
            get {
                return ResourceManager.GetString("Control_Framework_Category_already_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Category cannot be null.
        /// </summary>
        public static string Control_framework_category_cannot_be_null {
            get {
                return ResourceManager.GetString("Control_framework_category_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Category Clause cannot be null.
        /// </summary>
        public static string Control_framework_category_clause_cannot_be_null {
            get {
                return ResourceManager.GetString("Control_framework_category_clause_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Category Clause has been created successfully.
        /// </summary>
        public static string Control_Framework_Category_Clause_has_been_created_successfully {
            get {
                return ResourceManager.GetString("Control_Framework_Category_Clause_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Category Clause has been updated successfully.
        /// </summary>
        public static string Control_Framework_Category_Clause_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("Control_Framework_Category_Clause_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Category Clause is required.
        /// </summary>
        public static string Control_Framework_Category_Clause_is_required {
            get {
                return ResourceManager.GetString("Control_Framework_Category_Clause_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Category has been created successfully.
        /// </summary>
        public static string Control_Framework_Category_has_been_created_successfully {
            get {
                return ResourceManager.GetString("Control_Framework_Category_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Category has been updated successfully.
        /// </summary>
        public static string Control_Framework_Category_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("Control_Framework_Category_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Category Id is required.
        /// </summary>
        public static string Control_Framework_Category_Id_is_required {
            get {
                return ResourceManager.GetString("Control_Framework_Category_Id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Category Name already exist.
        /// </summary>
        public static string Control_Framework_Category_Name_already_exist {
            get {
                return ResourceManager.GetString("Control_Framework_Category_Name_already_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Category name is required.
        /// </summary>
        public static string Control_Framework_Category_name_is_required {
            get {
                return ResourceManager.GetString("Control_Framework_Category_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework has been created successfully.
        /// </summary>
        public static string Control_Framework_has_been_created_successfully {
            get {
                return ResourceManager.GetString("Control_Framework_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework has been updated successfully.
        /// </summary>
        public static string Control_Framework_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("Control_Framework_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Id is required.
        /// </summary>
        public static string Control_Framework_Id_is_required {
            get {
                return ResourceManager.GetString("Control_Framework_Id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework name is required.
        /// </summary>
        public static string Control_Framework_name_is_required {
            get {
                return ResourceManager.GetString("Control_Framework_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Control Framework was not found.
        /// </summary>
        public static string Control_framework_not_found {
            get {
                return ResourceManager.GetString("Control_framework_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Could not find the specified encryption key.
        /// </summary>
        public static string Could_not_find_encryption_key {
            get {
                return ResourceManager.GetString("Could_not_find_encryption_key", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to sort data in the request.
        /// </summary>
        public static string Could_not_sort_data {
            get {
                return ResourceManager.GetString("Could_not_sort_data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Couldn&apos;t find latest supplier submission request.
        /// </summary>
        public static string Couldn_t_find_latest_supplier_submission_request {
            get {
                return ResourceManager.GetString("Couldn_t_find_latest_supplier_submission_request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country already exist.
        /// </summary>
        public static string Country_already_exist {
            get {
                return ResourceManager.GetString("Country_already_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country exposure level is required.
        /// </summary>
        public static string Country_exposure_level_is_required {
            get {
                return ResourceManager.GetString("Country_exposure_level_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country has been created successfully.
        /// </summary>
        public static string Country_has_been_created_successfully {
            get {
                return ResourceManager.GetString("Country_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country has been updated successfully.
        /// </summary>
        public static string Country_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("Country_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country name is required.
        /// </summary>
        public static string Country_name_is_required {
            get {
                return ResourceManager.GetString("Country_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country not found.
        /// </summary>
        public static string Country_not_found {
            get {
                return ResourceManager.GetString("Country_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country revenue not found.
        /// </summary>
        public static string Country_revenue_not_found {
            get {
                return ResourceManager.GetString("Country_revenue_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Quote Request.
        /// </summary>
        public static string Create_quote_request {
            get {
                return ResourceManager.GetString("Create_quote_request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency code cannot be null or empty.
        /// </summary>
        public static string Currency_code_cannot_be_null_or_empty {
            get {
                return ResourceManager.GetString("Currency_code_cannot_be_null_or_empty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency not foud.
        /// </summary>
        public static string Currency_not_foud {
            get {
                return ResourceManager.GetString("Currency_not_foud", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency not found.
        /// </summary>
        public static string Currency_not_found {
            get {
                return ResourceManager.GetString("Currency_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data centre not found.
        /// </summary>
        public static string Data_centre_not_found {
            get {
                return ResourceManager.GetString("Data_centre_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data is not processable.
        /// </summary>
        public static string Data_is_not_processable {
            get {
                return ResourceManager.GetString("Data_is_not_processable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date of Birth is required.
        /// </summary>
        public static string Date_of_Birth_is_required {
            get {
                return ResourceManager.GetString("Date_of_Birth_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to days.
        /// </summary>
        public static string days {
            get {
                return ResourceManager.GetString("days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default lockout time Span is required.
        /// </summary>
        public static string Default_lockout_time_Span_is_required {
            get {
                return ResourceManager.GetString("Default_lockout_time_Span_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deletion of entity &quot;{0}&quot; ({1}) failed. {2}..
        /// </summary>
        public static string Deletion_of_entity_failed {
            get {
                return ResourceManager.GetString("Deletion_of_entity_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email &apos;{0}&apos; is already taken..
        /// </summary>
        public static string Email_is_already_taken {
            get {
                return ResourceManager.GetString("Email_is_already_taken", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email &apos;{0}&apos; is invalid..
        /// </summary>
        public static string Email_is_invalid {
            get {
                return ResourceManager.GetString("Email_is_invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Endorsement with the same name already exists.
        /// </summary>
        public static string Endorsement_with_the_same_name_already_exists {
            get {
                return ResourceManager.GetString("Endorsement_with_the_same_name_already_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Endorsement with the same name already exists..
        /// </summary>
        public static string Endorsement_with_the_same_name_already_exists_ {
            get {
                return ResourceManager.GetString("Endorsement_with_the_same_name_already_exists_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Endpoint is currently unavailable.
        /// </summary>
        public static string Endpoint_is_currently_unavailable {
            get {
                return ResourceManager.GetString("Endpoint_is_currently_unavailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entity &quot;{0}&quot; with key &quot;{1}&quot; was not found..
        /// </summary>
        public static string Entity_with_key_was_not_found {
            get {
                return ResourceManager.GetString("Entity_with_key_was_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error changing user name..
        /// </summary>
        public static string Error_changing_user_name {
            get {
                return ResourceManager.GetString("Error_changing_user_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error changing your user name..
        /// </summary>
        public static string Error_changing_your_user_name {
            get {
                return ResourceManager.GetString("Error_changing_your_user_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expected date not found.
        /// </summary>
        public static string Expected_date_not_found {
            get {
                return ResourceManager.GetString("Expected_date_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to create user.
        /// </summary>
        public static string Failed_to_create_user {
            get {
                return ResourceManager.GetString("Failed_to_create_user", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to update user.
        /// </summary>
        public static string Failed_to_update_user {
            get {
                return ResourceManager.GetString("Failed_to_update_user", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File has not been uploaded..
        /// </summary>
        public static string File_has_not_been_uploaded {
            get {
                return ResourceManager.GetString("File_has_not_been_uploaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File is empty..
        /// </summary>
        public static string File_is_empty {
            get {
                return ResourceManager.GetString("File_is_empty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File is invalid.
        /// </summary>
        public static string File_is_invalid {
            get {
                return ResourceManager.GetString("File_is_invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File is too large.
        /// </summary>
        public static string File_is_too_large {
            get {
                return ResourceManager.GetString("File_is_too_large", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File not found.
        /// </summary>
        public static string File_not_found {
            get {
                return ResourceManager.GetString("File_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File storage settings have been updated successfully..
        /// </summary>
        public static string File_storage_settings_have_been_updated_successfully {
            get {
                return ResourceManager.GetString("File_storage_settings_have_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First name is required..
        /// </summary>
        public static string First_name_is_required {
            get {
                return ResourceManager.GetString("First_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Follow market is in a state that doesn&apos;t allow unfollowing.
        /// </summary>
        public static string Follow_market_cant_unfollow {
            get {
                return ResourceManager.GetString("Follow_market_cant_unfollow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The form has already been signed.
        /// </summary>
        public static string Form_already_signed {
            get {
                return ResourceManager.GetString("Form_already_signed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Form has been updated successfully.
        /// </summary>
        public static string Form_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("Form_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The coverage limit for the head of cover cannot exceed the limit specified in the quote request.
        /// </summary>
        public static string Head_of_cover_limit_bigger_than_quote_request {
            get {
                return ResourceManager.GetString("Head_of_cover_limit_bigger_than_quote_request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Height is required..
        /// </summary>
        public static string Height_is_required {
            get {
                return ResourceManager.GetString("Height_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hrs.
        /// </summary>
        public static string Hrs {
            get {
                return ResourceManager.GetString("Hrs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity settings have been updated successfully..
        /// </summary>
        public static string Identity_settings_have_been_updated_successfully {
            get {
                return ResourceManager.GetString("Identity_settings_have_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inception.
        /// </summary>
        public static string Inception {
            get {
                return ResourceManager.GetString("Inception", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect password..
        /// </summary>
        public static string Incorrect_password {
            get {
                return ResourceManager.GetString("Incorrect_password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect services requested.
        /// </summary>
        public static string Incorrect_services_requested {
            get {
                return ResourceManager.GetString("Incorrect_services_requested", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry already exist.
        /// </summary>
        public static string Industry_already_exist {
            get {
                return ResourceManager.GetString("Industry_already_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry exposure level is required.
        /// </summary>
        public static string Industry_exposure_level_is_required {
            get {
                return ResourceManager.GetString("Industry_exposure_level_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry has been created successfully.
        /// </summary>
        public static string Industry_has_been_created_successfully {
            get {
                return ResourceManager.GetString("Industry_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry has been updated successfully.
        /// </summary>
        public static string Industry_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("Industry_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry name is required.
        /// </summary>
        public static string Industry_name_is_required {
            get {
                return ResourceManager.GetString("Industry_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry not found.
        /// </summary>
        public static string Industry_not_found {
            get {
                return ResourceManager.GetString("Industry_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry revenue not found.
        /// </summary>
        public static string Industry_revenue_not_found {
            get {
                return ResourceManager.GetString("Industry_revenue_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal.
        /// </summary>
        public static string Internal {
            get {
                return ResourceManager.GetString("Internal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal error with an email service occured.
        /// </summary>
        public static string Internal_error_with_an_email_service_occured {
            get {
                return ResourceManager.GetString("Internal_error_with_an_email_service_occured", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid applicant ID..
        /// </summary>
        public static string Invalid_applicant_Id {
            get {
                return ResourceManager.GetString("Invalid_applicant_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid application forms.
        /// </summary>
        public static string Invalid_application_forms {
            get {
                return ResourceManager.GetString("Invalid_application_forms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Application Form Id.
        /// </summary>
        public static string Invalid_ApplicationForm_Id {
            get {
                return ResourceManager.GetString("Invalid_ApplicationForm_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Application Form Page Id.
        /// </summary>
        public static string Invalid_ApplicationForm_Page_Id {
            get {
                return ResourceManager.GetString("Invalid_ApplicationForm_Page_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid authenticator code entered..
        /// </summary>
        public static string Invalid_authenticator_code_entered {
            get {
                return ResourceManager.GetString("Invalid_authenticator_code_entered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Choice Id.
        /// </summary>
        public static string Invalid_Choice_Id {
            get {
                return ResourceManager.GetString("Invalid_Choice_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid client request..
        /// </summary>
        public static string Invalid_client_request {
            get {
                return ResourceManager.GetString("Invalid_client_request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The passed parameters have not been validated.
        /// </summary>
        public static string Invalid_command_parameters {
            get {
                return ResourceManager.GetString("Invalid_command_parameters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Control Framework Category Clause Id.
        /// </summary>
        public static string Invalid_Control_Framework_Category_Clause_Id {
            get {
                return ResourceManager.GetString("Invalid_Control_Framework_Category_Clause_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Control Framework Category Id.
        /// </summary>
        public static string Invalid_Control_Framework_Category_Id {
            get {
                return ResourceManager.GetString("Invalid_Control_Framework_Category_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Control Framework Id.
        /// </summary>
        public static string Invalid_Control_Framework_Id {
            get {
                return ResourceManager.GetString("Invalid_Control_Framework_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid country Id.
        /// </summary>
        public static string Invalid_country_Id {
            get {
                return ResourceManager.GetString("Invalid_country_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid currency.
        /// </summary>
        public static string Invalid_currency {
            get {
                return ResourceManager.GetString("Invalid_currency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid data found.
        /// </summary>
        public static string Invalid_data_found {
            get {
                return ResourceManager.GetString("Invalid_data_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid file extension.
        /// </summary>
        public static string Invalid_file_extension {
            get {
                return ResourceManager.GetString("Invalid_file_extension", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid file signature.
        /// </summary>
        public static string Invalid_file_signature {
            get {
                return ResourceManager.GetString("Invalid_file_signature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid file storage Id..
        /// </summary>
        public static string Invalid_file_storage_Id {
            get {
                return ResourceManager.GetString("Invalid_file_storage_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid industry Id.
        /// </summary>
        public static string Invalid_industry_Id {
            get {
                return ResourceManager.GetString("Invalid_industry_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid lockout settings Id..
        /// </summary>
        public static string Invalid_lockout_settings_Id {
            get {
                return ResourceManager.GetString("Invalid_lockout_settings_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid login attempt..
        /// </summary>
        public static string Invalid_login_attempt {
            get {
                return ResourceManager.GetString("Invalid_login_attempt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid loss type Id.
        /// </summary>
        public static string Invalid_loss_type_Id {
            get {
                return ResourceManager.GetString("Invalid_loss_type_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid password settings Id..
        /// </summary>
        public static string Invalid_password_settings_Id {
            get {
                return ResourceManager.GetString("Invalid_password_settings_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid permission used.
        /// </summary>
        public static string Invalid_permission_used {
            get {
                return ResourceManager.GetString("Invalid_permission_used", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Question Id.
        /// </summary>
        public static string Invalid_Question_Id {
            get {
                return ResourceManager.GetString("Invalid_Question_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid recovery code entered..
        /// </summary>
        public static string Invalid_recovery_code_entered {
            get {
                return ResourceManager.GetString("Invalid_recovery_code_entered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid region Id.
        /// </summary>
        public static string Invalid_region_Id {
            get {
                return ResourceManager.GetString("Invalid_region_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid report Id..
        /// </summary>
        public static string Invalid_report_Id {
            get {
                return ResourceManager.GetString("Invalid_report_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid role.
        /// </summary>
        public static string Invalid_role {
            get {
                return ResourceManager.GetString("Invalid_role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid role ID..
        /// </summary>
        public static string Invalid_role_Id {
            get {
                return ResourceManager.GetString("Invalid_role_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid sign in settings Id..
        /// </summary>
        public static string Invalid_sign_in_settings_Id {
            get {
                return ResourceManager.GetString("Invalid_sign_in_settings_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Status.
        /// </summary>
        public static string Invalid_Status {
            get {
                return ResourceManager.GetString("Invalid_Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid supplier details.
        /// </summary>
        public static string Invalid_supplier_details {
            get {
                return ResourceManager.GetString("Invalid_supplier_details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid suppliers.
        /// </summary>
        public static string Invalid_suppliers {
            get {
                return ResourceManager.GetString("Invalid_suppliers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Tenant Id.
        /// </summary>
        public static string Invalid_tenant_Id {
            get {
                return ResourceManager.GetString("Invalid_tenant_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid tenant name..
        /// </summary>
        public static string Invalid_tenant_name {
            get {
                return ResourceManager.GetString("Invalid_tenant_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid token..
        /// </summary>
        public static string Invalid_token {
            get {
                return ResourceManager.GetString("Invalid_token", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid token settings Id..
        /// </summary>
        public static string Invalid_token_settings_Id {
            get {
                return ResourceManager.GetString("Invalid_token_settings_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Provided URL is invalid.
        /// </summary>
        public static string Invalid_url_payload {
            get {
                return ResourceManager.GetString("Invalid_url_payload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid user ID..
        /// </summary>
        public static string Invalid_user_Id {
            get {
                return ResourceManager.GetString("Invalid_user_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid user settings Id.
        /// </summary>
        public static string Invalid_user_settings_Id {
            get {
                return ResourceManager.GetString("Invalid_user_settings_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid username or password.
        /// </summary>
        public static string Invalid_username_or_password {
            get {
                return ResourceManager.GetString("Invalid_username_or_password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job title is required..
        /// </summary>
        public static string Job_title_is_required {
            get {
                return ResourceManager.GetString("Job_title_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last name is required.
        /// </summary>
        public static string Last_name_is_required {
            get {
                return ResourceManager.GetString("Last_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Layer not found.
        /// </summary>
        public static string Layer_not_found {
            get {
                return ResourceManager.GetString("Layer_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Size exceeds broker limit.
        /// </summary>
        public static string Line_Size_exceeds_broker_limit {
            get {
                return ResourceManager.GetString("Line_Size_exceeds_broker_limit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line size exceeds maximum line size.
        /// </summary>
        public static string Line_size_exceeds_maximum_line_size {
            get {
                return ResourceManager.GetString("Line_size_exceeds_maximum_line_size", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Size exceeds underwriter limit.
        /// </summary>
        public static string Line_Size_exceeds_underwriter_limit {
            get {
                return ResourceManager.GetString("Line_Size_exceeds_underwriter_limit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lockout is not enabled for this user..
        /// </summary>
        public static string Lockout_is_not_enabled_for_this_user {
            get {
                return ResourceManager.GetString("Lockout_is_not_enabled_for_this_user", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loss type already exist.
        /// </summary>
        public static string Loss_type_already_exist {
            get {
                return ResourceManager.GetString("Loss_type_already_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loss type has been created successfully.
        /// </summary>
        public static string Loss_type_has_been_created_successfully {
            get {
                return ResourceManager.GetString("Loss_type_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loss type has been updated successfully.
        /// </summary>
        public static string Loss_type_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("Loss_type_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loss type is required.
        /// </summary>
        public static string Loss_type_is_required {
            get {
                return ResourceManager.GetString("Loss_type_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max failed access attempt is required..
        /// </summary>
        public static string Max_failed_access_attempt_is_required {
            get {
                return ResourceManager.GetString("Max_failed_access_attempt_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum allowed number of attachments must not exceed 3..
        /// </summary>
        public static string Maximum_allowed_number_of_attachments_must_not_exceed_3 {
            get {
                return ResourceManager.GetString("Maximum_allowed_number_of_attachments_must_not_exceed_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Message not found.
        /// </summary>
        public static string Message_not_found {
            get {
                return ResourceManager.GetString("Message_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Method cannot be null..
        /// </summary>
        public static string Method_cannot_be_null {
            get {
                return ResourceManager.GetString("Method_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minimum allowed number of attachments must be at least 1..
        /// </summary>
        public static string Minimum_allowed_number_of_attachments_must_be_at_least_1 {
            get {
                return ResourceManager.GetString("Minimum_allowed_number_of_attachments_must_be_at_least_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to N/A.
        /// </summary>
        public static string N_A {
            get {
                return ResourceManager.GetString("N_A", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The provided Submission is neither Wholesale or Organisation.
        /// </summary>
        public static string Neither_wholesale_or_organisation {
            get {
                return ResourceManager.GetString("Neither_wholesale_or_organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New password is required..
        /// </summary>
        public static string New_password_is_required {
            get {
                return ResourceManager.GetString("New_password_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New password must be at least 6 characters..
        /// </summary>
        public static string New_password_must_be_at_least_6_characters {
            get {
                return ResourceManager.GetString("New_password_must_be_at_least_6_characters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New password must not exceed 200 characters..
        /// </summary>
        public static string New_password_must_not_exceed_200_characters {
            get {
                return ResourceManager.GetString("New_password_must_not_exceed_200_characters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No agreed option found for this layer.
        /// </summary>
        public static string No_agreed_option_found_for_this_layer {
            get {
                return ResourceManager.GetString("No_agreed_option_found_for_this_layer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No answers scores found.
        /// </summary>
        public static string No_answer_scores_found {
            get {
                return ResourceManager.GetString("No_answer_scores_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No available brokers found.
        /// </summary>
        public static string No_available_brokers_found {
            get {
                return ResourceManager.GetString("No_available_brokers_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No changes were made.
        /// </summary>
        public static string No_changes_made {
            get {
                return ResourceManager.GetString("No_changes_made", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Data.
        /// </summary>
        public static string No_Data {
            get {
                return ResourceManager.GetString("No_Data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No form data found.
        /// </summary>
        public static string No_form_data_found {
            get {
                return ResourceManager.GetString("No_form_data_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Name Available.
        /// </summary>
        public static string No_Name_Available {
            get {
                return ResourceManager.GetString("No_Name_Available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No question choices located, this WILL affect the score continuining on.
        /// </summary>
        public static string No_question_choices {
            get {
                return ResourceManager.GetString("No_question_choices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No score over 0 was found.
        /// </summary>
        public static string No_score_over_zero_found {
            get {
                return ResourceManager.GetString("No_score_over_zero_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No tenant id provided.
        /// </summary>
        public static string No_tenant_id_provided {
            get {
                return ResourceManager.GetString("No_tenant_id_provided", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not all required fields are provided.
        /// </summary>
        public static string Not_all_required_fields_provided {
            get {
                return ResourceManager.GetString("Not_all_required_fields_provided", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Covered.
        /// </summary>
        public static string Not_covered {
            get {
                return ResourceManager.GetString("Not_covered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not found.
        /// </summary>
        public static string Not_found {
            get {
                return ResourceManager.GetString("Not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notification not found.
        /// </summary>
        public static string Notification_not_found {
            get {
                return ResourceManager.GetString("Notification_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Old password is required..
        /// </summary>
        public static string Old_password_is_required {
            get {
                return ResourceManager.GetString("Old_password_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to One of permission types is not mapped to a permission.
        /// </summary>
        public static string One_of_permission_types_is_not_mapped_to_a_permission {
            get {
                return ResourceManager.GetString("One_of_permission_types_is_not_mapped_to_a_permission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to One of the selected users can&apos;t be assigned.
        /// </summary>
        public static string One_of_the_selected_users_can_t_be_assigned {
            get {
                return ResourceManager.GetString("One_of_the_selected_users_can_t_be_assigned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only 5 contracts are required.
        /// </summary>
        public static string Only_5_contracts_are_required {
            get {
                return ResourceManager.GetString("Only_5_contracts_are_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only those between the ages of 18 and 28 are allowed for enlisting..
        /// </summary>
        public static string Only_those_between_the_ages_of_18_and_28_are_allowed_for_enlisting {
            get {
                return ResourceManager.GetString("Only_those_between_the_ages_of_18_and_28_are_allowed_for_enlisting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only those who weigh between 50 and 200 with normal BMI are allowed for enlisting..
        /// </summary>
        public static string Only_those_who_weigh_between_50_and_200_with_normal_BMI_are_allowed_for_enlisting {
            get {
                return ResourceManager.GetString("Only_those_who_weigh_between_50_and_200_with_normal_BMI_are_allowed_for_enlisting" +
                        "", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only those whose BMI between 18.5 and 24.9 are allowed for enlisting..
        /// </summary>
        public static string Only_those_whose_BMI_between_18_5_and_24_9_are_allowed_for_enlisting {
            get {
                return ResourceManager.GetString("Only_those_whose_BMI_between_18_5_and_24_9_are_allowed_for_enlisting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only those whose heights between 100 and 250 with normal BMI are allowed for enlisting..
        /// </summary>
        public static string Only_those_whose_heights_between_100_and_250_with_normal_BMI_are_allowed_for_enlisting {
            get {
                return ResourceManager.GetString("Only_those_whose_heights_between_100_and_250_with_normal_BMI_are_allowed_for_enli" +
                        "sting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Optimistic concurrency failure, object has been modified..
        /// </summary>
        public static string Optimistic_concurrency_failure {
            get {
                return ResourceManager.GetString("Optimistic_concurrency_failure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Option not found.
        /// </summary>
        public static string Option_not_found {
            get {
                return ResourceManager.GetString("Option_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation already exists.
        /// </summary>
        public static string Organisation_already_exists {
            get {
                return ResourceManager.GetString("Organisation_already_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation Name.
        /// </summary>
        public static string Organisation_Name {
            get {
                return ResourceManager.GetString("Organisation_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation not found.
        /// </summary>
        public static string Organisation_not_found {
            get {
                return ResourceManager.GetString("Organisation_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation request not found.
        /// </summary>
        public static string Organisation_request_not_found {
            get {
                return ResourceManager.GetString("Organisation_request_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Placement.
        /// </summary>
        public static string Part_Placement {
            get {
                return ResourceManager.GetString("Part_Placement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password cannot contain &apos;password&apos;..
        /// </summary>
        public static string Password_cannot_contain_password {
            get {
                return ResourceManager.GetString("Password_cannot_contain_password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password cannot contain username..
        /// </summary>
        public static string Password_cannot_contain_username {
            get {
                return ResourceManager.GetString("Password_cannot_contain_username", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password is required..
        /// </summary>
        public static string Password_is_required {
            get {
                return ResourceManager.GetString("Password_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password must be at least 6 characters..
        /// </summary>
        public static string Password_must_be_at_least_6_characters {
            get {
                return ResourceManager.GetString("Password_must_be_at_least_6_characters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password must not exceed 200 characters..
        /// </summary>
        public static string Password_must_not_exceed_200_characters {
            get {
                return ResourceManager.GetString("Password_must_not_exceed_200_characters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password reset link was sent. Please check your email..
        /// </summary>
        public static string Password_reset_link_was_sent {
            get {
                return ResourceManager.GetString("Password_reset_link_was_sent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passwords must be at least {0} characters..
        /// </summary>
        public static string Passwords_must_be_at_least_length_characters {
            get {
                return ResourceManager.GetString("Passwords_must_be_at_least_length_characters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passwords must have at least one digit (&apos;0&apos;-&apos;9&apos;)..
        /// </summary>
        public static string Passwords_must_have_at_least_one_digit {
            get {
                return ResourceManager.GetString("Passwords_must_have_at_least_one_digit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passwords must have at least one lowercase (&apos;a&apos;-&apos;z&apos;)..
        /// </summary>
        public static string Passwords_must_have_at_least_one_lowercase {
            get {
                return ResourceManager.GetString("Passwords_must_have_at_least_one_lowercase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passwords must have at least one non alphanumeric character..
        /// </summary>
        public static string Passwords_must_have_at_least_one_non_alphanumeric_character {
            get {
                return ResourceManager.GetString("Passwords_must_have_at_least_one_non_alphanumeric_character", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passwords must have at least one uppercase (&apos;A&apos;-&apos;Z&apos;)..
        /// </summary>
        public static string Passwords_must_have_at_least_one_uppercase {
            get {
                return ResourceManager.GetString("Passwords_must_have_at_least_one_uppercase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Percent.
        /// </summary>
        public static string Percent {
            get {
                return ResourceManager.GetString("Percent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone number is_required..
        /// </summary>
        public static string Phone_number_is_required {
            get {
                return ResourceManager.GetString("Phone_number_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please confirm your account by &lt;a href=&apos;{0}&apos;&gt;clicking here&lt;/a&gt;..
        /// </summary>
        public static string Please_confirm_your_account_by_clicking_here {
            get {
                return ResourceManager.GetString("Please_confirm_your_account_by_clicking_here", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please confirm your email..
        /// </summary>
        public static string Please_confirm_your_email {
            get {
                return ResourceManager.GetString("Please_confirm_your_email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please reset your password by &lt;a href=&apos;{0}&apos;&gt;clicking here&lt;/a&gt;..
        /// </summary>
        public static string Please_reset_your_password_by_clicking_here {
            get {
                return ResourceManager.GetString("Please_reset_your_password_by_clicking_here", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please specify the application tenant mode..
        /// </summary>
        public static string Please_specify_the_application_tenant_mode {
            get {
                return ResourceManager.GetString("Please_specify_the_application_tenant_mode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Primary.
        /// </summary>
        public static string Primary {
            get {
                return ResourceManager.GetString("Primary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product already exists.
        /// </summary>
        public static string Product_already_exists {
            get {
                return ResourceManager.GetString("Product_already_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product in use and cannot be deleted.
        /// </summary>
        public static string Product_in_use {
            get {
                return ResourceManager.GetString("Product_in_use", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profile picture is required..
        /// </summary>
        public static string Profile_picture_is_required {
            get {
                return ResourceManager.GetString("Profile_picture_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question has been created successfully.
        /// </summary>
        public static string Question_has_been_created_successfully {
            get {
                return ResourceManager.GetString("Question_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question has been deleted successfully.
        /// </summary>
        public static string Question_has_been_deleted_successfully {
            get {
                return ResourceManager.GetString("Question_has_been_deleted_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question has been updated successfully.
        /// </summary>
        public static string Question_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("Question_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question Id is required.
        /// </summary>
        public static string Question_Id_is_required {
            get {
                return ResourceManager.GetString("Question_Id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question is required.
        /// </summary>
        public static string Question_is_required {
            get {
                return ResourceManager.GetString("Question_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question must be approved in order to add an answer.
        /// </summary>
        public static string Question_must_be_approved_in_order_to_add_an_answer {
            get {
                return ResourceManager.GetString("Question_must_be_approved_in_order_to_add_an_answer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question not found.
        /// </summary>
        public static string Question_not_found {
            get {
                return ResourceManager.GetString("Question_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question page Id is required.
        /// </summary>
        public static string Question_Page_Id_is_required {
            get {
                return ResourceManager.GetString("Question_Page_Id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question title is required.
        /// </summary>
        public static string Question_title_is_required {
            get {
                return ResourceManager.GetString("Question_title_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quota Share Follower must be in Accepted status.
        /// </summary>
        public static string Quota_Share_Follower_must_be_in_Accepted_status {
            get {
                return ResourceManager.GetString("Quota_Share_Follower_must_be_in_Accepted_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quota share is in a state that prevents from accepting or declining it.
        /// </summary>
        public static string Quota_share_is_in_a_state_that_prevents_from_accepting_or_declining_it {
            get {
                return ResourceManager.GetString("Quota_share_is_in_a_state_that_prevents_from_accepting_or_declining_it", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote has a status that does not allow option selection.
        /// </summary>
        public static string Quote_has_a_status_that_does_not_allow_option_selection {
            get {
                return ResourceManager.GetString("Quote_has_a_status_that_does_not_allow_option_selection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recovery Code is required..
        /// </summary>
        public static string Recovery_Code_is_required {
            get {
                return ResourceManager.GetString("Recovery_Code_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recovery Code Redemption Failed.
        /// </summary>
        public static string Recovery_Code_Redemption_Failed {
            get {
                return ResourceManager.GetString("Recovery_Code_Redemption_Failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference name is required..
        /// </summary>
        public static string Reference_name_is_required {
            get {
                return ResourceManager.GetString("Reference_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh token timespan cannot be null..
        /// </summary>
        public static string Refresh_token_timespan_cannot_be_null {
            get {
                return ResourceManager.GetString("Refresh_token_timespan_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh token timespan is required..
        /// </summary>
        public static string Refresh_token_timespan_is_required {
            get {
                return ResourceManager.GetString("Refresh_token_timespan_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh token timespan must be greater than access token expiry time..
        /// </summary>
        public static string Refresh_token_timespan_must_be_greater_than_access_token_expiry_time {
            get {
                return ResourceManager.GetString("Refresh_token_timespan_must_be_greater_than_access_token_expiry_time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region already exist.
        /// </summary>
        public static string Region_already_exist {
            get {
                return ResourceManager.GetString("Region_already_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region exposure level is required.
        /// </summary>
        public static string Region_exposure_level_is_required {
            get {
                return ResourceManager.GetString("Region_exposure_level_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region has been created successfully.
        /// </summary>
        public static string Region_has_been_created_successfully {
            get {
                return ResourceManager.GetString("Region_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region has been updated successfully.
        /// </summary>
        public static string Region_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("Region_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region Id is required.
        /// </summary>
        public static string Region_Id_is_required {
            get {
                return ResourceManager.GetString("Region_Id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region name is required.
        /// </summary>
        public static string Region_name_is_required {
            get {
                return ResourceManager.GetString("Region_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 111111111 is not a valid Social security number..
        /// </summary>
        public static string Repeated_Ones_are_not_valid_Social_security_number {
            get {
                return ResourceManager.GetString("Repeated_Ones_are_not_valid_Social_security_number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 333333333 is not a valid Social security number..
        /// </summary>
        public static string Repeated_Threes_are_not_valid_Social_security_number {
            get {
                return ResourceManager.GetString("Repeated_Threes_are_not_valid_Social_security_number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report cannot be null for analysis.
        /// </summary>
        public static string Report_cannot_be_null {
            get {
                return ResourceManager.GetString("Report_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reporting failed as value is null.
        /// </summary>
        public static string Report_failed {
            get {
                return ResourceManager.GetString("Report_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request doesn&apos;t exist.
        /// </summary>
        public static string Request_doesn_t_exist {
            get {
                return ResourceManager.GetString("Request_doesn_t_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request originated from an invalid application.
        /// </summary>
        public static string Request_originated_from_an_invalid_application {
            get {
                return ResourceManager.GetString("Request_originated_from_an_invalid_application", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required length is required..
        /// </summary>
        public static string Required_length_is_required {
            get {
                return ResourceManager.GetString("Required_length_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required unique characters is required..
        /// </summary>
        public static string Required_unique_characters_is_required {
            get {
                return ResourceManager.GetString("Required_unique_characters_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset your password.
        /// </summary>
        public static string Reset_your_password {
            get {
                return ResourceManager.GetString("Reset_your_password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role has been created successfully..
        /// </summary>
        public static string Role_has_been_created_successfully {
            get {
                return ResourceManager.GetString("Role_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role has been deleted successfully..
        /// </summary>
        public static string Role_has_been_deleted_successfully {
            get {
                return ResourceManager.GetString("Role_has_been_deleted_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role has been updated successfully..
        /// </summary>
        public static string Role_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("Role_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role name &apos;{0}&apos; is already taken..
        /// </summary>
        public static string Role_name_is_already_taken {
            get {
                return ResourceManager.GetString("Role_name_is_already_taken", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role name &apos;{0}&apos; is invalid..
        /// </summary>
        public static string Role_name_is_invalid {
            get {
                return ResourceManager.GetString("Role_name_is_invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role name is required..
        /// </summary>
        public static string Role_name_is_required {
            get {
                return ResourceManager.GetString("Role_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role not found.
        /// </summary>
        public static string Role_not_found {
            get {
                return ResourceManager.GetString("Role_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service is not nullable.
        /// </summary>
        public static string Service_is_not_nullable {
            get {
                return ResourceManager.GetString("Service_is_not_nullable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Social security number is required.
        /// </summary>
        public static string Social_security_number_is_required {
            get {
                return ResourceManager.GetString("Social_security_number_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Social security number must contain only 9-digits..
        /// </summary>
        public static string Social_security_number_must_contain_only_9_digits {
            get {
                return ResourceManager.GetString("Social_security_number_must_contain_only_9_digits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Social security number must not contain consecutive digits..
        /// </summary>
        public static string Social_security_number_must_not_contain_consecutive_digits {
            get {
                return ResourceManager.GetString("Social_security_number_must_not_contain_consecutive_digits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status change is invalid.
        /// </summary>
        public static string Status_change_is_invalid {
            get {
                return ResourceManager.GetString("Status_change_is_invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Storage type is required..
        /// </summary>
        public static string Storage_type_is_required {
            get {
                return ResourceManager.GetString("Storage_type_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subjectivity with the same name already exists.
        /// </summary>
        public static string Subjectivity_with_the_same_name_already_exists {
            get {
                return ResourceManager.GetString("Subjectivity_with_the_same_name_already_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The submitted application form is not linked to a submission.
        /// </summary>
        public static string Submission_and_form_not_linked {
            get {
                return ResourceManager.GetString("Submission_and_form_not_linked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submission cannot be null.
        /// </summary>
        public static string Submission_cannot_be_null {
            get {
                return ResourceManager.GetString("Submission_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submission not found.
        /// </summary>
        public static string Submission_not_found {
            get {
                return ResourceManager.GetString("Submission_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finishing submission request requires all forms to be completed.
        /// </summary>
        public static string Submission_request_cant_be_finished {
            get {
                return ResourceManager.GetString("Submission_request_cant_be_finished", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submission request not found.
        /// </summary>
        public static string Submission_Request_Not_Found {
            get {
                return ResourceManager.GetString("Submission_Request_Not_Found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier already exists.
        /// </summary>
        public static string Supplier_already_exists {
            get {
                return ResourceManager.GetString("Supplier_already_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Could not find Supplier Application Form.
        /// </summary>
        public static string Supplier_applicationn_form_version_not_found {
            get {
                return ResourceManager.GetString("Supplier_applicationn_form_version_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier is Not Found.
        /// </summary>
        public static string Supplier_is_not_found {
            get {
                return ResourceManager.GetString("Supplier_is_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier not found.
        /// </summary>
        public static string Supplier_not_found {
            get {
                return ResourceManager.GetString("Supplier_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier not found or your organisation is not associated with it.
        /// </summary>
        public static string Supplier_not_found_or_your_organisation_is_not_associated_with_it {
            get {
                return ResourceManager.GetString("Supplier_not_found_or_your_organisation_is_not_associated_with_it", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Surname is required..
        /// </summary>
        public static string Surname_is_required {
            get {
                return ResourceManager.GetString("Surname_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Survey answers malformed and cannot deseralise.
        /// </summary>
        public static string Survey_answers_malformed {
            get {
                return ResourceManager.GetString("Survey_answers_malformed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Survey data is missing.
        /// </summary>
        public static string Survey_data_missing {
            get {
                return ResourceManager.GetString("Survey_data_missing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TBC.
        /// </summary>
        public static string TBC {
            get {
                return ResourceManager.GetString("TBC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tenant has been created successfully..
        /// </summary>
        public static string Tenant_has_been_created_successfully {
            get {
                return ResourceManager.GetString("Tenant_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tenant has been deleted successfully..
        /// </summary>
        public static string Tenant_has_been_deleted_successfully {
            get {
                return ResourceManager.GetString("Tenant_has_been_deleted_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tenant has been updated successfully..
        /// </summary>
        public static string Tenant_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("Tenant_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tenant header has to have a value.
        /// </summary>
        public static string Tenant_header_needs_value {
            get {
                return ResourceManager.GetString("Tenant_header_needs_value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tenant name is required..
        /// </summary>
        public static string Tenant_name_is_required {
            get {
                return ResourceManager.GetString("Tenant_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tenant with this subdomain already exists.
        /// </summary>
        public static string Tenant_with_this_subdomain_already_exists {
            get {
                return ResourceManager.GetString("Tenant_with_this_subdomain_already_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The applicant is not found..
        /// </summary>
        public static string The_applicant_is_not_found {
            get {
                return ResourceManager.GetString("The_applicant_is_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Application Form Page was not found.
        /// </summary>
        public static string The_ApplicationForm_Page_was_not_found {
            get {
                return ResourceManager.GetString("The_ApplicationForm_Page_was_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Choice was not found.
        /// </summary>
        public static string The_Choice_was_not_found {
            get {
                return ResourceManager.GetString("The_Choice_was_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The code is invalid.
        /// </summary>
        public static string The_code_is_invalid {
            get {
                return ResourceManager.GetString("The_code_is_invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The organisation already has a tenant.
        /// </summary>
        public static string The_organisation_already_has_a_tenant {
            get {
                return ResourceManager.GetString("The_organisation_already_has_a_tenant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The password and confirmation password do not match..
        /// </summary>
        public static string The_password_and_confirmation_password_do_not_match {
            get {
                return ResourceManager.GetString("The_password_and_confirmation_password_do_not_match", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Question was not found.
        /// </summary>
        public static string The_Question_was_not_found {
            get {
                return ResourceManager.GetString("The_Question_was_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The specified role is already registered..
        /// </summary>
        public static string The_specified_role_is_already_registered {
            get {
                return ResourceManager.GetString("The_specified_role_is_already_registered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The specified role is already registered in the given tenant..
        /// </summary>
        public static string The_specified_role_is_already_registered_in_the_given_tenant {
            get {
                return ResourceManager.GetString("The_specified_role_is_already_registered_in_the_given_tenant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The specified username and email are already registered..
        /// </summary>
        public static string The_specified_username_and_email_are_already_registered {
            get {
                return ResourceManager.GetString("The_specified_username_and_email_are_already_registered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The specified username and email are already registered in the given tenant..
        /// </summary>
        public static string The_specified_username_and_email_are_already_registered_in_the_given_tenant {
            get {
                return ResourceManager.GetString("The_specified_username_and_email_are_already_registered_in_the_given_tenant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Tenant was not found.
        /// </summary>
        public static string The_tenant_was_not_found {
            get {
                return ResourceManager.GetString("The_tenant_was_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The user has no 2FA enabled..
        /// </summary>
        public static string The_user_has_no_2FA_enabled {
            get {
                return ResourceManager.GetString("The_user_has_no_2FA_enabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The worksheet was not found.
        /// </summary>
        public static string The_worksheet_was_not_found {
            get {
                return ResourceManager.GetString("The_worksheet_was_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Theia Admin.
        /// </summary>
        public static string Theia_Admin {
            get {
                return ResourceManager.GetString("Theia_Admin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Theia Json Format is not valid.
        /// </summary>
        public static string Theia_json_cannot_be_null {
            get {
                return ResourceManager.GetString("Theia_json_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Token settings have been updated successfully..
        /// </summary>
        public static string Token_settings_have_been_updated_successfully {
            get {
                return ResourceManager.GetString("Token_settings_have_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open analysis.
        /// </summary>
        public static string Tooltip_open_analysis {
            get {
                return ResourceManager.GetString("Tooltip_open_analysis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top.
        /// </summary>
        public static string Top {
            get {
                return ResourceManager.GetString("Top", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top 1%.
        /// </summary>
        public static string Top_1_percent {
            get {
                return ResourceManager.GetString("Top_1_percent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Two factor authentication code is required..
        /// </summary>
        public static string Two_factor_authentication_code_is_required {
            get {
                return ResourceManager.GetString("Two_factor_authentication_code_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Two factor authentication code must be at least 6 character long..
        /// </summary>
        public static string Two_factor_authentication_code_must_be_at_least_6_character_long {
            get {
                return ResourceManager.GetString("Two_factor_authentication_code_must_be_at_least_6_character_long", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Two factor authentication code must not exceed 7 characters..
        /// </summary>
        public static string Two_factor_authentication_code_must_not_exceed_7_characters {
            get {
                return ResourceManager.GetString("Two_factor_authentication_code_must_not_exceed_7_characters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2Fa has been disabled. You can reenable Two-Factor Authentication when you setup an authenticator app..
        /// </summary>
        public static string Two_factor_authentication_has_been_disabled {
            get {
                return ResourceManager.GetString("Two_factor_authentication_has_been_disabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Two-factor authentication required..
        /// </summary>
        public static string Two_factor_authentication_required {
            get {
                return ResourceManager.GetString("Two_factor_authentication_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to add static role.
        /// </summary>
        public static string Unable_to_add_static_role {
            get {
                return ResourceManager.GetString("Unable_to_add_static_role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to change visibility of a wholesale submission&apos;s layer.
        /// </summary>
        public static string Unable_to_change_visibility_of_a_wholesale_submission_s_layer {
            get {
                return ResourceManager.GetString("Unable_to_change_visibility_of_a_wholesale_submission_s_layer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to create new tenant in single tenant mode..
        /// </summary>
        public static string Unable_to_create_new_tenant_in_single_tenant_mode {
            get {
                return ResourceManager.GetString("Unable_to_create_new_tenant_in_single_tenant_mode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to delete static role..
        /// </summary>
        public static string Unable_to_delete_static_role {
            get {
                return ResourceManager.GetString("Unable_to_delete_static_role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to delete static user..
        /// </summary>
        public static string Unable_to_delete_static_user {
            get {
                return ResourceManager.GetString("Unable_to_delete_static_user", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to enable 2FA..
        /// </summary>
        public static string Unable_to_enable_2FA {
            get {
                return ResourceManager.GetString("Unable_to_enable_2FA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load applicant..
        /// </summary>
        public static string Unable_to_load_applicant {
            get {
                return ResourceManager.GetString("Unable_to_load_applicant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load Application Form Page.
        /// </summary>
        public static string Unable_to_load_ApplicationForm_Page {
            get {
                return ResourceManager.GetString("Unable_to_load_ApplicationForm_Page", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load Choice.
        /// </summary>
        public static string Unable_to_load_Choice {
            get {
                return ResourceManager.GetString("Unable_to_load_Choice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load Control Framework.
        /// </summary>
        public static string Unable_to_load_Control_Framework {
            get {
                return ResourceManager.GetString("Unable_to_load_Control_Framework", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load Control Framework Category.
        /// </summary>
        public static string Unable_to_load_Control_Framework_Category {
            get {
                return ResourceManager.GetString("Unable_to_load_Control_Framework_Category", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load Control Framework Category Clause.
        /// </summary>
        public static string Unable_to_load_Control_Framework_Category_Clause {
            get {
                return ResourceManager.GetString("Unable_to_load_Control_Framework_Category_Clause", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load country.
        /// </summary>
        public static string Unable_to_load_country {
            get {
                return ResourceManager.GetString("Unable_to_load_country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load form.
        /// </summary>
        public static string Unable_to_load_form {
            get {
                return ResourceManager.GetString("Unable_to_load_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load industry.
        /// </summary>
        public static string Unable_to_load_industry {
            get {
                return ResourceManager.GetString("Unable_to_load_industry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load loss type.
        /// </summary>
        public static string Unable_to_load_loss_type {
            get {
                return ResourceManager.GetString("Unable_to_load_loss_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load Question.
        /// </summary>
        public static string Unable_to_load_Question {
            get {
                return ResourceManager.GetString("Unable_to_load_Question", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load region.
        /// </summary>
        public static string Unable_to_load_region {
            get {
                return ResourceManager.GetString("Unable_to_load_region", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load report..
        /// </summary>
        public static string Unable_to_load_report {
            get {
                return ResourceManager.GetString("Unable_to_load_report", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load role..
        /// </summary>
        public static string Unable_to_load_role {
            get {
                return ResourceManager.GetString("Unable_to_load_role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load Tenant.
        /// </summary>
        public static string Unable_to_load_tenant {
            get {
                return ResourceManager.GetString("Unable_to_load_tenant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to load user..
        /// </summary>
        public static string Unable_to_load_user {
            get {
                return ResourceManager.GetString("Unable_to_load_user", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to reset authenticator keys..
        /// </summary>
        public static string Unable_to_reset_authenticator_keys {
            get {
                return ResourceManager.GetString("Unable_to_reset_authenticator_keys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to update static role.
        /// </summary>
        public static string Unable_to_update_static_role {
            get {
                return ResourceManager.GetString("Unable_to_update_static_role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to update static user..
        /// </summary>
        public static string Unable_to_update_static_user {
            get {
                return ResourceManager.GetString("Unable_to_update_static_user", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Underwriters.
        /// </summary>
        public static string Underwriters {
            get {
                return ResourceManager.GetString("Underwriters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unexpected error occured when downloading the file.
        /// </summary>
        public static string Unexpected_error_occured_when_downloading_the_file {
            get {
                return ResourceManager.GetString("Unexpected_error_occured_when_downloading_the_file", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unexpected error occurred deleting user with ID &apos;{0}&apos;..
        /// </summary>
        public static string Unexpected_error_occurred_deleting_user_with_Id {
            get {
                return ResourceManager.GetString("Unexpected_error_occurred_deleting_user_with_Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unexpected error occurred disabling Two-Factor Authentication for user with ID &apos;{0}&apos;..
        /// </summary>
        public static string Unexpected_error_occurred_disabling_2FA {
            get {
                return ResourceManager.GetString("Unexpected_error_occurred_disabling_2FA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quota share is incomplete, so max line size is unknown.
        /// </summary>
        public static string UpdateQuotaShareFollowerStatus_HandleAsync_Quota_share_is_incomplete__so_max_line_size_is_unknown {
            get {
                return ResourceManager.GetString("UpdateQuotaShareFollowerStatus_HandleAsync_Quota_share_is_incomplete__so_max_line" +
                        "_size_is_unknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This URL has expired and cannot be re-used.
        /// </summary>
        public static string Url_has_expired {
            get {
                return ResourceManager.GetString("Url_has_expired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User already has a password set..
        /// </summary>
        public static string User_already_has_a_password_set {
            get {
                return ResourceManager.GetString("User_already_has_a_password_set", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User already in role &apos;{0}&apos;..
        /// </summary>
        public static string User_already_in_role {
            get {
                return ResourceManager.GetString("User_already_in_role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User does not belong to any supplier.
        /// </summary>
        public static string User_does_not_belong_to_any_supplier {
            get {
                return ResourceManager.GetString("User_does_not_belong_to_any_supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User has been added without files..
        /// </summary>
        public static string User_has_been_added_without_files {
            get {
                return ResourceManager.GetString("User_has_been_added_without_files", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User has been created successfully..
        /// </summary>
        public static string User_has_been_created_successfully {
            get {
                return ResourceManager.GetString("User_has_been_created_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User has been deleted successfully..
        /// </summary>
        public static string User_has_been_deleted_successfully {
            get {
                return ResourceManager.GetString("User_has_been_deleted_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User has been updated successfully..
        /// </summary>
        public static string User_has_been_updated_successfully {
            get {
                return ResourceManager.GetString("User_has_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User has been updated successfully without updating his/her roles as the user is static type..
        /// </summary>
        public static string User_has_been_updated_successfully_without_updating_his_her_roles_as_the_user_is_static_type {
            get {
                return ResourceManager.GetString("User_has_been_updated_successfully_without_updating_his_her_roles_as_the_user_is_" +
                        "static_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Id is required..
        /// </summary>
        public static string User_Id_is_required {
            get {
                return ResourceManager.GetString("User_Id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The specified user could not be found.
        /// </summary>
        public static string User_is_invalid {
            get {
                return ResourceManager.GetString("User_is_invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User permissions have been updated successfully..
        /// </summary>
        public static string User_permissions_have_been_updated_successfully {
            get {
                return ResourceManager.GetString("User_permissions_have_been_updated_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User with ID &apos;{0}&apos; has been deleted..
        /// </summary>
        public static string User_with_Id_deleted {
            get {
                return ResourceManager.GetString("User_with_Id_deleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User with ID &apos;{0}&apos; logged in with Two-Factor Authentication..
        /// </summary>
        public static string User_with_Id_UserId_logged_in_with_2Fa {
            get {
                return ResourceManager.GetString("User_with_Id_UserId_logged_in_with_2Fa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username has been confirmed successfully..
        /// </summary>
        public static string Username_has_been_confirmed_successfully {
            get {
                return ResourceManager.GetString("Username_has_been_confirmed_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username &apos;{0}&apos; is already taken..
        /// </summary>
        public static string Username_is_already_taken {
            get {
                return ResourceManager.GetString("Username_is_already_taken", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username &apos;{0}&apos; is invalid, can only contain letters or digits..
        /// </summary>
        public static string Username_is_invalid {
            get {
                return ResourceManager.GetString("Username_is_invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username is required..
        /// </summary>
        public static string Username_is_required {
            get {
                return ResourceManager.GetString("Username_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username must be at least 6 characters..
        /// </summary>
        public static string Username_must_be_at_least_6_characters {
            get {
                return ResourceManager.GetString("Username_must_be_at_least_6_characters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username must not exceed 200 characters..
        /// </summary>
        public static string Username_must_not_exceed_200_characters {
            get {
                return ResourceManager.GetString("Username_must_not_exceed_200_characters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value cannot be empty.
        /// </summary>
        public static string Value_cannot_be_empty {
            get {
                return ResourceManager.GetString("Value_cannot_be_empty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} cannot be null.
        /// </summary>
        public static string value_cannot_be_null {
            get {
                return ResourceManager.GetString("value_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor is already associated with the organisation.
        /// </summary>
        public static string Vendor_is_already_associated_with_the_organisation {
            get {
                return ResourceManager.GetString("Vendor_is_already_associated_with_the_organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor Type not found.
        /// </summary>
        public static string Vendor_type_not_found {
            get {
                return ResourceManager.GetString("Vendor_type_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verification code is invalid..
        /// </summary>
        public static string Verification_code_is_invalid {
            get {
                return ResourceManager.GetString("Verification_code_is_invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verification email has been sent. Please check your email..
        /// </summary>
        public static string Verification_email_has_been_sent {
            get {
                return ResourceManager.GetString("Verification_email_has_been_sent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version.
        /// </summary>
        public static string Version {
            get {
                return ResourceManager.GetString("Version", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version not found.
        /// </summary>
        public static string Version_not_found {
            get {
                return ResourceManager.GetString("Version_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Weight is required..
        /// </summary>
        public static string Weight_is_required {
            get {
                return ResourceManager.GetString("Weight_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wholesale Submission Not Found.
        /// </summary>
        public static string Wholesale_submission_not_found {
            get {
                return ResourceManager.GetString("Wholesale_submission_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are forbidden to access {0}..
        /// </summary>
        public static string You_are_forbidden {
            get {
                return ResourceManager.GetString("You_are_forbidden", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are forbidden to access this resource.
        /// </summary>
        public static string You_are_forbidden_to_access_this_resource {
            get {
                return ResourceManager.GetString("You_are_forbidden_to_access_this_resource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You&apos;re locked out..
        /// </summary>
        public static string You_are_locked_out {
            get {
                return ResourceManager.GetString("You_are_locked_out", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are not allowed to see underlyings.
        /// </summary>
        public static string You_are_not_allowed_to_see_underlyings {
            get {
                return ResourceManager.GetString("You_are_not_allowed_to_see_underlyings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are not authorized to access {0}..
        /// </summary>
        public static string You_are_not_authorized {
            get {
                return ResourceManager.GetString("You_are_not_authorized", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You do not have permission to edit this user.
        /// </summary>
        public static string You_do_not_have_permission_to_edit_this_user {
            get {
                return ResourceManager.GetString("You_do_not_have_permission_to_edit_this_user", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have generated new recovery codes..
        /// </summary>
        public static string You_have_generated_new_recovery_codes {
            get {
                return ResourceManager.GetString("You_have_generated_new_recovery_codes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have successfully created a new account..
        /// </summary>
        public static string You_have_successfully_created_a_new_account {
            get {
                return ResourceManager.GetString("You_have_successfully_created_a_new_account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your account is deactivated. Please contact your administrator..
        /// </summary>
        public static string Your_account_is_deactivated_Please_contact_your_administrator {
            get {
                return ResourceManager.GetString("Your_account_is_deactivated_Please_contact_your_administrator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your authenticator app has been verified..
        /// </summary>
        public static string Your_authenticator_app_has_been_verified {
            get {
                return ResourceManager.GetString("Your_authenticator_app_has_been_verified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your authenticator app key has been reset, you will need to configure your authenticator app using the new key..
        /// </summary>
        public static string Your_authenticator_app_key_has_been_reset {
            get {
                return ResourceManager.GetString("Your_authenticator_app_key_has_been_reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your email has been changed successfully..
        /// </summary>
        public static string Your_email_has_been_changed_successfully {
            get {
                return ResourceManager.GetString("Your_email_has_been_changed_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your email has been successfully changed..
        /// </summary>
        public static string Your_email_has_been_successfully_changed {
            get {
                return ResourceManager.GetString("Your_email_has_been_successfully_changed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your email is unchanged..
        /// </summary>
        public static string Your_email_is_unchanged {
            get {
                return ResourceManager.GetString("Your_email_is_unchanged", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your password has been changed..
        /// </summary>
        public static string Your_password_has_been_changed {
            get {
                return ResourceManager.GetString("Your_password_has_been_changed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your password has been reset..
        /// </summary>
        public static string Your_password_has_been_reset {
            get {
                return ResourceManager.GetString("Your_password_has_been_reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your password has been set..
        /// </summary>
        public static string Your_password_has_been_set {
            get {
                return ResourceManager.GetString("Your_password_has_been_set", resourceCulture);
            }
        }
    }
}
