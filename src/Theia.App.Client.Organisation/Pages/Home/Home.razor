@using Theia.App.Client.Common.Extensions
@using Theia.App.Shared.Organisation.HomePage
@using Theia.Infrastructure.Common.Permissions

<PageHeader Header="@Resource.Dashboard"/>

<TelerikGridLayout ColumnSpacing="1em" RowSpacing="1em" Class="home-page-layout">
    <GridLayoutItems>
        <GridLayoutItem Row="1" Column="1" ColumnSpan="12">
            <TelerikCard>
                <CardBody>
                    <h2>
                        <span id="organisation-name">@_vm.OrganisationName</span>
                    </h2>
                </CardBody>
            </TelerikCard>
        </GridLayoutItem>
        <GridLayoutItem Row="2" Column="1" ColumnSpan="4">
            <SingleInfoCard 
                Value="@_vm.UnansweredQuestionsText"
                Title="@Resource.Unanswered_Forum_Questions"
                Url="@CommonUrlConstants.ViewControlAssessments"
                UrlTitle="@Resource.Submission"
                IsValid="@_vm.UnansweredQuestionsValid"
                Color="SingleInfoCard.InfoCardColor.Green"/>
        </GridLayoutItem>
        <GridLayoutItem Row="2" Column="5" ColumnSpan="4">
            <SingleInfoCard 
                Value="@_vm.IncompleteAssessmentsText"
                Title="@Resource.Incomplete_Assessments" 
                Url="@CommonUrlConstants.ViewControlAssessments" 
                UrlTitle="@Resource.ApplicationForms"
                IsValid="@_vm.IncompleteAssessmentsValid"
                Color="SingleInfoCard.InfoCardColor.DarkBlue"/>
        </GridLayoutItem>
        <GridLayoutItem Row="2" Column="9" ColumnSpan="4">
            <AuthorizeView Context="authContext">
                <Authorized>
                    @{
                        string url = authContext.User.HasPermission(PermissionTypes.GetOrgProfile) ? UrlConstants.ProfileSummary : string.Empty;
                    }
                    
                    <SingleInfoCard
                        Value="@_vm.PercentageCompletedText"
                        Title="@Resource.Organisation_Profile_Complete"
                        Url="@url"
                        UrlTitle="@Resource.Organisation_Profile"
                        IsValid="@_vm.IsPercentageCompletedValid"
                        Color="SingleInfoCard.InfoCardColor.Purple"/>
                </Authorized>
            </AuthorizeView>
        </GridLayoutItem>
        <GridLayoutItem Row="3" Column="1">
            <Summary
                Class="summary-card"
                UrlSafeSubmissionId="@_vm.LatestSubmissionSafeId"
                Cts="@_cts"></Summary>
        </GridLayoutItem>
        <GridLayoutItem Row="3" Column="2" ColumnSpan="11">
            <ControlFrameworks
                UrlSafeSubmissionId="@_vm.LatestSubmissionSafeId"
                ControlFrameworksToDisplay="@_vm.Frameworks"
                GaugesAreClickable="false"
                PercentilePositionText="@_vm.PercentagePosition"
                _cts="@_cts"/>
        </GridLayoutItem>
        <GridLayoutItem Row="4" Column="1" ColumnSpan="6">
            <TelerikCard>
                <CardHeader>
                    <CardTitle>@Resource.Brokers_that_have_access_to_your_most_recent_submission</CardTitle>
                </CardHeader>
                <CardBody>
                    <TelerikGrid
                        TItem="GetHomePageInfoUserWithAccess"
                        Data="@_vm.Brokers">
                        <GridColumns>
                            <GridColumn Field="@nameof(GetHomePageInfoUserWithAccess.TenantName)" Title="@Resource.Broking_House"/>
                            <GridColumn Field="@nameof(GetHomePageInfoUserWithAccess.UserFullName)" Title="@Resource.Broker"/>
                        </GridColumns>
                        <NoDataTemplate>
                            <span>@Resource.No_broker_has_access_to_your_most_recent_control_assessment</span>
                        </NoDataTemplate>
                    </TelerikGrid>
                </CardBody>
            </TelerikCard>
        </GridLayoutItem>
        <GridLayoutItem Row="4" Column="7" ColumnSpan="6">
            <TelerikCard>
                <CardHeader>
                    <CardTitle>@Resource.Underwriters_that_have_access_to_your_most_recent_submission</CardTitle>
                </CardHeader>
                <CardBody>
                    <TelerikGrid
                        TItem="GetHomePageInfoUserWithAccess"
                        Data="@_vm.Underwriters">
                        <GridColumns>
                            <GridColumn Field="@nameof(GetHomePageInfoUserWithAccess.TenantName)" Title="@Resource.Insurer"/>
                            <GridColumn Field="@nameof(GetHomePageInfoUserWithAccess.UserFullName)" Title="@Resource.Underwriter"/>
                        </GridColumns>
                        <NoDataTemplate>
                            <span>@Resource.No_underwriter_has_access_to_your_most_recent_control_assessment</span>
                        </NoDataTemplate>
                    </TelerikGrid>
                </CardBody>
            </TelerikCard>
        </GridLayoutItem>
    </GridLayoutItems>
</TelerikGridLayout>

<style>
    #organisation-name {
        font-weight: 500;
        color: #0891b2;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
</style>