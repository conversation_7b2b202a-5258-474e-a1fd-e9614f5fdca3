using Theia.Domain.Entities.Identity;

namespace Theia.Domain.Entities.Organisations.Profile.Shared;

public class Country
{
    public Guid Id { get; init; }
    public string? Name { get; set; }
    public required ExposureLevel ExposureLevel { get; set; }
    public required bool IsActive { get; set; }
    public string? CurrencyCode { get; init; }

    public Guid RegionId { get; set; }
    public Region? Region { get; init; }

    public List<Organisation> Organisations { get; init; } = [];
}